"""SQLAlchemy implementation of exercise repository.

Implements the exercise repository interface using SQLAlchemy ORM
with support for versioning, soft-delete, and audit trails.
"""

from datetime import datetime
from uuid import UUID, uuid4

from sqlalchemy import and_, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ....domain.entities.exercise import (
    AuditAction,
    Exercise,
    ExerciseAuditLog,
    ExerciseMedia,
    ExerciseSearchFilters,
)
from ....domain.repositories.exercise_repository import ExerciseRepository
from ..models.exercise_model import (
    ExerciseAuditLogModel,
    ExerciseMediaModel,
    ExerciseModel,
)


class ExerciseRepositoryImpl(ExerciseRepository):
    """SQLAlchemy implementation of exercise repository."""

    def __init__(self, session: AsyncSession):
        """Initialize repository with database session."""
        self.session = session

    def _get_enum_value(self, enum_value):
        """Safely get enum value, handling both enum objects and string values."""
        if hasattr(enum_value, 'value'):
            return enum_value.value
        return enum_value

    def _get_enum_list_values(self, enum_list):
        """Safely get enum values from a list, handling both enum objects and string values."""
        if not enum_list:
            return None
        return [self._get_enum_value(item) for item in enum_list]

    async def create(self, exercise: Exercise) -> Exercise:
        """Create a new exercise."""
        # Generate new IDs if not provided
        if not exercise.id:
            exercise.id = uuid4()
        if not exercise.exercise_uuid:
            exercise.exercise_uuid = uuid4()

        # Create model instance
        model = ExerciseModel(
            id=exercise.id,
            exercise_uuid=exercise.exercise_uuid,
            version=exercise.version,
            is_current_version=exercise.is_current_version,
            parent_version_id=exercise.parent_version_id,
            name=exercise.name,
            description=exercise.description,
            primary_muscle_group=self._get_enum_value(exercise.primary_muscle_group),
            secondary_muscle_groups=self._get_enum_list_values(exercise.secondary_muscle_groups),
            movement_pattern=self._get_enum_value(exercise.movement_pattern),
            equipment_required=self._get_enum_list_values(exercise.equipment_required),
            difficulty_level=self._get_enum_value(exercise.difficulty_level),
            video_url=exercise.video_url,
            thumbnail_url=exercise.thumbnail_url,
            form_cues=exercise.form_cues,
            setup_instructions=exercise.setup_instructions,
            execution_steps=exercise.execution_steps,
            common_mistakes=exercise.common_mistakes,
            safety_notes=exercise.safety_notes,
            is_active=exercise.is_active,
            is_approved=exercise.is_approved,
            approval_status=self._get_enum_value(exercise.approval_status),
            created_by=exercise.created_by,
            updated_by=exercise.updated_by,
            approved_by=exercise.approved_by,
            created_at=exercise.created_at or datetime.utcnow(),
            updated_at=exercise.updated_at or datetime.utcnow(),
            deleted_at=exercise.deleted_at,
            approved_at=exercise.approved_at,
            version_notes=exercise.version_notes,
            change_reason=self._get_enum_value(exercise.change_reason),
        )

        self.session.add(model)
        await self.session.flush()

        # Log audit trail
        await self.log_audit(
            exercise_id=model.id,
            exercise_uuid=model.exercise_uuid,
            action=AuditAction.CREATED,
            user_id=exercise.created_by,
            new_values=self._model_to_dict(model),
        )

        return self._model_to_entity(model)

    async def get_by_id(self, exercise_id: UUID) -> Exercise | None:
        """Get exercise by ID."""
        stmt = (
            select(ExerciseModel)
            .options(selectinload(ExerciseModel.media))
            .where(ExerciseModel.id == exercise_id)
        )
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()

        return self._model_to_entity(model) if model else None

    async def get_by_uuid(
        self, exercise_uuid: UUID, version: int | None = None, current_only: bool = True
    ) -> Exercise | None:
        """Get exercise by UUID and optionally version."""
        stmt = (
            select(ExerciseModel)
            .options(selectinload(ExerciseModel.media))
            .where(ExerciseModel.exercise_uuid == exercise_uuid)
        )

        if version is not None:
            stmt = stmt.where(ExerciseModel.version == version)
        elif current_only:
            stmt = stmt.where(ExerciseModel.is_current_version)

        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()

        return self._model_to_entity(model) if model else None

    async def get_versions(self, exercise_uuid: UUID) -> list[Exercise]:
        """Get all versions of an exercise."""
        stmt = (
            select(ExerciseModel)
            .options(selectinload(ExerciseModel.media))
            .where(ExerciseModel.exercise_uuid == exercise_uuid)
            .order_by(ExerciseModel.version)
        )
        result = await self.session.execute(stmt)
        models = result.scalars().all()

        return [self._model_to_entity(model) for model in models]

    async def search(
        self, filters: ExerciseSearchFilters, limit: int = 50, offset: int = 0
    ) -> list[Exercise]:
        """Search exercises with filters."""
        stmt = select(ExerciseModel).options(selectinload(ExerciseModel.media))

        # Apply filters
        stmt = self._apply_search_filters(stmt, filters)

        # Apply pagination
        stmt = stmt.limit(limit).offset(offset)

        # Order by name
        stmt = stmt.order_by(ExerciseModel.name)

        result = await self.session.execute(stmt)
        models = result.scalars().all()

        return [self._model_to_entity(model) for model in models]

    async def count(self, filters: ExerciseSearchFilters) -> int:
        """Count exercises matching filters."""
        stmt = select(func.count(ExerciseModel.id))

        # Apply filters
        stmt = self._apply_search_filters(stmt, filters)

        result = await self.session.execute(stmt)
        return result.scalar() or 0

    def _apply_search_filters(self, stmt, filters: ExerciseSearchFilters):
        """Apply search filters to query."""
        # Current version only
        if filters.current_version_only:
            stmt = stmt.where(ExerciseModel.is_current_version)

        # Include deleted
        if not filters.include_deleted:
            stmt = stmt.where(ExerciseModel.deleted_at.is_(None))

        # Name search
        if filters.name:
            stmt = stmt.where(ExerciseModel.name.ilike(f"%{filters.name}%"))

        # Primary muscle group
        if filters.primary_muscle_group:
            stmt = stmt.where(
                ExerciseModel.primary_muscle_group == self._get_enum_value(filters.primary_muscle_group)
            )

        # Movement pattern
        if filters.movement_pattern:
            stmt = stmt.where(
                ExerciseModel.movement_pattern == self._get_enum_value(filters.movement_pattern)
            )

        # Difficulty level
        if filters.difficulty_level:
            stmt = stmt.where(
                ExerciseModel.difficulty_level == self._get_enum_value(filters.difficulty_level)
            )

        # Active status
        if filters.is_active is not None:
            stmt = stmt.where(ExerciseModel.is_active == filters.is_active)

        # Approved status
        if filters.is_approved is not None:
            stmt = stmt.where(ExerciseModel.is_approved == filters.is_approved)

        # Approval status
        if filters.approval_status:
            stmt = stmt.where(
                ExerciseModel.approval_status == self._get_enum_value(filters.approval_status)
            )

        # Created by
        if filters.created_by:
            stmt = stmt.where(ExerciseModel.created_by == filters.created_by)

        return stmt

    async def update(self, exercise: Exercise) -> Exercise:
        """Update an exercise (creates new version)."""
        # Get current exercise
        current = await self.get_by_id(exercise.id)
        if not current:
            raise ValueError(f"Exercise with ID {exercise.id} not found")

        # Update the model
        stmt = (
            update(ExerciseModel)
            .where(ExerciseModel.id == exercise.id)
            .values(
                name=exercise.name,
                description=exercise.description,
                primary_muscle_group=self._get_enum_value(exercise.primary_muscle_group),
                secondary_muscle_groups=self._get_enum_list_values(exercise.secondary_muscle_groups),
                movement_pattern=self._get_enum_value(exercise.movement_pattern),
                equipment_required=self._get_enum_list_values(exercise.equipment_required),
                difficulty_level=self._get_enum_value(exercise.difficulty_level),
                video_url=exercise.video_url,
                thumbnail_url=exercise.thumbnail_url,
                form_cues=exercise.form_cues,
                setup_instructions=exercise.setup_instructions,
                execution_steps=exercise.execution_steps,
                common_mistakes=exercise.common_mistakes,
                safety_notes=exercise.safety_notes,
                is_active=exercise.is_active,
                is_approved=exercise.is_approved,
                approval_status=self._get_enum_value(exercise.approval_status),
                updated_by=exercise.updated_by,
                updated_at=datetime.utcnow(),
                version_notes=exercise.version_notes,
                change_reason=self._get_enum_value(exercise.change_reason),
            )
        )

        await self.session.execute(stmt)

        # Log audit trail
        await self.log_audit(
            exercise_id=exercise.id,
            exercise_uuid=exercise.exercise_uuid,
            action=AuditAction.UPDATED,
            user_id=exercise.updated_by,
            old_values=self._entity_to_dict(current),
            new_values=self._entity_to_dict(exercise),
        )

        return await self.get_by_id(exercise.id)

    async def soft_delete(
        self, exercise_id: UUID, deleted_by: UUID | None = None
    ) -> bool:
        """Soft delete an exercise."""
        stmt = (
            update(ExerciseModel)
            .where(ExerciseModel.id == exercise_id)
            .values(
                deleted_at=datetime.utcnow(),
                updated_by=deleted_by,
                updated_at=datetime.utcnow(),
            )
        )

        result = await self.session.execute(stmt)

        if result.rowcount > 0:
            # Get exercise for audit log
            exercise = await self.get_by_id(exercise_id)
            if exercise:
                await self.log_audit(
                    exercise_id=exercise_id,
                    exercise_uuid=exercise.exercise_uuid,
                    action=AuditAction.DELETED,
                    user_id=deleted_by,
                )
            return True

        return False

    async def approve(
        self, exercise_id: UUID, approved_by: UUID, notes: str | None = None
    ) -> Exercise:
        """Approve an exercise."""
        stmt = (
            update(ExerciseModel)
            .where(ExerciseModel.id == exercise_id)
            .values(
                is_approved=True,
                approval_status="approved",
                approved_by=approved_by,
                approved_at=datetime.utcnow(),
                updated_by=approved_by,
                updated_at=datetime.utcnow(),
            )
        )

        await self.session.execute(stmt)

        # Get updated exercise
        exercise = await self.get_by_id(exercise_id)
        if exercise:
            await self.log_audit(
                exercise_id=exercise_id,
                exercise_uuid=exercise.exercise_uuid,
                action=AuditAction.APPROVED,
                user_id=approved_by,
                notes=notes,
            )

        return exercise

    async def reject(
        self, exercise_id: UUID, rejected_by: UUID, notes: str | None = None
    ) -> Exercise:
        """Reject an exercise."""
        stmt = (
            update(ExerciseModel)
            .where(ExerciseModel.id == exercise_id)
            .values(
                is_approved=False,
                approval_status="rejected",
                updated_by=rejected_by,
                updated_at=datetime.utcnow(),
            )
        )

        await self.session.execute(stmt)

        # Get updated exercise
        exercise = await self.get_by_id(exercise_id)
        if exercise:
            await self.log_audit(
                exercise_id=exercise_id,
                exercise_uuid=exercise.exercise_uuid,
                action=AuditAction.REJECTED,
                user_id=rejected_by,
                notes=notes,
            )

        return exercise

    async def create_version(
        self,
        exercise_uuid: UUID,
        updated_exercise: Exercise,
        created_by: UUID | None = None,
    ) -> Exercise:
        """Create a new version of an existing exercise."""
        # Get current version
        current = await self.get_current_version(exercise_uuid)
        if not current:
            raise ValueError(f"Exercise with UUID {exercise_uuid} not found")

        # Mark current version as not current
        await self.session.execute(
            update(ExerciseModel)
            .where(
                and_(
                    ExerciseModel.exercise_uuid == exercise_uuid,
                    ExerciseModel.is_current_version,
                )
            )
            .values(is_current_version=False)
        )

        # Create new version
        new_version = Exercise(
            id=uuid4(),
            exercise_uuid=exercise_uuid,
            version=current.version + 1,
            is_current_version=True,
            parent_version_id=current.id,
            name=updated_exercise.name,
            description=updated_exercise.description,
            primary_muscle_group=updated_exercise.primary_muscle_group,
            secondary_muscle_groups=updated_exercise.secondary_muscle_groups,
            movement_pattern=updated_exercise.movement_pattern,
            equipment_required=updated_exercise.equipment_required,
            difficulty_level=updated_exercise.difficulty_level,
            video_url=updated_exercise.video_url,
            thumbnail_url=updated_exercise.thumbnail_url,
            form_cues=updated_exercise.form_cues,
            setup_instructions=updated_exercise.setup_instructions,
            execution_steps=updated_exercise.execution_steps,
            common_mistakes=updated_exercise.common_mistakes,
            safety_notes=updated_exercise.safety_notes,
            is_active=updated_exercise.is_active,
            is_approved=False,  # New versions need approval
            approval_status=updated_exercise.approval_status,
            created_by=created_by,
            updated_by=created_by,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            version_notes=updated_exercise.version_notes,
            change_reason=updated_exercise.change_reason,
        )

        return await self.create(new_version)

    async def get_current_version(self, exercise_uuid: UUID) -> Exercise | None:
        """Get the current version of an exercise."""
        return await self.get_by_uuid(exercise_uuid, current_only=True)

    async def set_current_version(
        self, exercise_uuid: UUID, version: int, updated_by: UUID | None = None
    ) -> bool:
        """Set the current version of an exercise."""
        # Mark all versions as not current
        await self.session.execute(
            update(ExerciseModel)
            .where(ExerciseModel.exercise_uuid == exercise_uuid)
            .values(is_current_version=False)
        )

        # Mark specified version as current
        result = await self.session.execute(
            update(ExerciseModel)
            .where(
                and_(
                    ExerciseModel.exercise_uuid == exercise_uuid,
                    ExerciseModel.version == version,
                )
            )
            .values(
                is_current_version=True,
                updated_by=updated_by,
                updated_at=datetime.utcnow(),
            )
        )

        return result.rowcount > 0

    # Media management methods
    async def add_media(self, exercise_id: UUID, media: ExerciseMedia) -> ExerciseMedia:
        """Add media to an exercise."""
        model = ExerciseMediaModel(
            id=media.id or uuid4(),
            exercise_id=exercise_id,
            media_type=self._get_enum_value(media.media_type),
            url=media.url,
            thumbnail_url=media.thumbnail_url,
            title=media.title,
            description=media.description,
            file_size_bytes=media.file_size_bytes,
            duration_seconds=media.duration_seconds,
            width_pixels=media.width_pixels,
            height_pixels=media.height_pixels,
            mime_type=media.mime_type,
            sort_order=media.sort_order,
            is_primary=media.is_primary,
            is_active=media.is_active,
            created_at=media.created_at or datetime.utcnow(),
            updated_at=media.updated_at or datetime.utcnow(),
        )

        self.session.add(model)
        await self.session.flush()

        return self._media_model_to_entity(model)

    async def get_media(self, exercise_id: UUID) -> list[ExerciseMedia]:
        """Get all media for an exercise."""
        stmt = (
            select(ExerciseMediaModel)
            .where(ExerciseMediaModel.exercise_id == exercise_id)
            .where(ExerciseMediaModel.is_active)
            .order_by(ExerciseMediaModel.sort_order, ExerciseMediaModel.created_at)
        )
        result = await self.session.execute(stmt)
        models = result.scalars().all()

        return [self._media_model_to_entity(model) for model in models]

    async def update_media(self, media_id: UUID, media: ExerciseMedia) -> ExerciseMedia:
        """Update exercise media."""
        stmt = (
            update(ExerciseMediaModel)
            .where(ExerciseMediaModel.id == media_id)
            .values(
                url=media.url,
                thumbnail_url=media.thumbnail_url,
                title=media.title,
                description=media.description,
                file_size_bytes=media.file_size_bytes,
                duration_seconds=media.duration_seconds,
                width_pixels=media.width_pixels,
                height_pixels=media.height_pixels,
                mime_type=media.mime_type,
                sort_order=media.sort_order,
                is_primary=media.is_primary,
                is_active=media.is_active,
                updated_at=datetime.utcnow(),
            )
        )

        await self.session.execute(stmt)

        # Get updated media
        updated_stmt = select(ExerciseMediaModel).where(
            ExerciseMediaModel.id == media_id
        )
        result = await self.session.execute(updated_stmt)
        model = result.scalar_one_or_none()

        return self._media_model_to_entity(model) if model else None

    async def delete_media(self, media_id: UUID) -> bool:
        """Delete exercise media."""
        stmt = (
            update(ExerciseMediaModel)
            .where(ExerciseMediaModel.id == media_id)
            .values(is_active=False, updated_at=datetime.utcnow())
        )

        result = await self.session.execute(stmt)
        return result.rowcount > 0

    # Audit trail methods
    async def log_audit(
        self,
        exercise_id: UUID,
        exercise_uuid: UUID,
        action: AuditAction,
        user_id: UUID | None = None,
        ip_address: str | None = None,
        user_agent: str | None = None,
        field_changes: dict | None = None,
        old_values: dict | None = None,
        new_values: dict | None = None,
        notes: str | None = None,
    ) -> ExerciseAuditLog:
        """Log an audit trail entry."""
        model = ExerciseAuditLogModel(
            id=uuid4(),
            exercise_id=exercise_id,
            exercise_uuid=exercise_uuid,
            action=self._get_enum_value(action),
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            field_changes=field_changes,
            old_values=old_values,
            new_values=new_values,
            notes=notes,
            created_at=datetime.utcnow(),
        )

        self.session.add(model)
        await self.session.flush()

        return self._audit_model_to_entity(model)

    async def get_audit_log(
        self, exercise_uuid: UUID, limit: int = 50, offset: int = 0
    ) -> list[ExerciseAuditLog]:
        """Get audit log for an exercise."""
        stmt = (
            select(ExerciseAuditLogModel)
            .where(ExerciseAuditLogModel.exercise_uuid == exercise_uuid)
            .order_by(desc(ExerciseAuditLogModel.created_at))
            .limit(limit)
            .offset(offset)
        )
        result = await self.session.execute(stmt)
        models = result.scalars().all()

        return [self._audit_model_to_entity(model) for model in models]

    async def exists(self, exercise_id: UUID) -> bool:
        """Check if exercise exists."""
        stmt = select(func.count(ExerciseModel.id)).where(
            ExerciseModel.id == exercise_id
        )
        result = await self.session.execute(stmt)
        return (result.scalar() or 0) > 0

    async def exists_by_name(self, name: str, exclude_id: UUID | None = None) -> bool:
        """Check if exercise with name exists."""
        stmt = (
            select(func.count(ExerciseModel.id))
            .where(ExerciseModel.name.ilike(name))
            .where(ExerciseModel.is_current_version)
            .where(ExerciseModel.deleted_at.is_(None))
        )

        if exclude_id:
            stmt = stmt.where(ExerciseModel.id != exclude_id)

        result = await self.session.execute(stmt)
        return (result.scalar() or 0) > 0

    # Helper methods for model/entity conversion
    def _model_to_entity(self, model: ExerciseModel) -> Exercise | None:
        """Convert model to entity."""
        if model is None:
            return None

        from ....domain.entities.exercise import (
            ApprovalStatus,
            ChangeReason,
            DifficultyLevel,
            Equipment,
            MovementPattern,
            MuscleGroup,
        )

        return Exercise(
            id=model.id,
            exercise_uuid=model.exercise_uuid,
            version=model.version,
            is_current_version=model.is_current_version,
            parent_version_id=model.parent_version_id,
            name=model.name,
            description=model.description,
            primary_muscle_group=MuscleGroup(model.primary_muscle_group),
            secondary_muscle_groups=(
                [MuscleGroup(mg) for mg in model.secondary_muscle_groups]
                if model.secondary_muscle_groups
                else None
            ),
            movement_pattern=MovementPattern(model.movement_pattern),
            equipment_required=(
                [Equipment(eq) for eq in model.equipment_required]
                if model.equipment_required
                else None
            ),
            difficulty_level=DifficultyLevel(model.difficulty_level),
            video_url=model.video_url,
            thumbnail_url=model.thumbnail_url,
            form_cues=model.form_cues,
            setup_instructions=model.setup_instructions,
            execution_steps=model.execution_steps,
            common_mistakes=model.common_mistakes,
            safety_notes=model.safety_notes,
            is_active=model.is_active,
            is_approved=model.is_approved,
            approval_status=ApprovalStatus(model.approval_status),
            created_by=model.created_by,
            updated_by=model.updated_by,
            approved_by=model.approved_by,
            created_at=model.created_at,
            updated_at=model.updated_at,
            deleted_at=model.deleted_at,
            approved_at=model.approved_at,
            version_notes=model.version_notes,
            change_reason=ChangeReason(model.change_reason),
            media=(
                [self._media_model_to_entity(media) for media in model.media]
                if model.media
                else None
            ),
        )

    def _media_model_to_entity(self, model: ExerciseMediaModel) -> ExerciseMedia:
        """Convert media model to entity."""
        from ....domain.entities.exercise import MediaType

        return ExerciseMedia(
            id=model.id,
            exercise_id=model.exercise_id,
            media_type=MediaType(model.media_type),
            url=model.url,
            thumbnail_url=model.thumbnail_url,
            title=model.title,
            description=model.description,
            file_size_bytes=model.file_size_bytes,
            duration_seconds=model.duration_seconds,
            width_pixels=model.width_pixels,
            height_pixels=model.height_pixels,
            mime_type=model.mime_type,
            sort_order=model.sort_order,
            is_primary=model.is_primary or False,  # Handle None values
            is_active=model.is_active,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )

    def _audit_model_to_entity(self, model: ExerciseAuditLogModel) -> ExerciseAuditLog:
        """Convert audit model to entity."""
        return ExerciseAuditLog(
            id=model.id,
            exercise_id=model.exercise_id,
            exercise_uuid=model.exercise_uuid,
            action=AuditAction(model.action),
            user_id=model.user_id,
            ip_address=model.ip_address,
            user_agent=model.user_agent,
            field_changes=model.field_changes,
            old_values=model.old_values,
            new_values=model.new_values,
            notes=model.notes,
            created_at=model.created_at,
        )

    def _model_to_dict(self, model: ExerciseModel) -> dict:
        """Convert model to dictionary for audit logging."""
        return {
            "id": str(model.id),
            "exercise_uuid": str(model.exercise_uuid),
            "version": model.version,
            "name": model.name,
            "description": model.description,
            "primary_muscle_group": model.primary_muscle_group,
            "secondary_muscle_groups": model.secondary_muscle_groups,
            "movement_pattern": model.movement_pattern,
            "equipment_required": model.equipment_required,
            "difficulty_level": model.difficulty_level,
            "is_active": model.is_active,
            "is_approved": model.is_approved,
            "approval_status": model.approval_status,
        }

    def _entity_to_dict(self, entity: Exercise) -> dict:
        """Convert entity to dictionary for audit logging."""
        if entity is None:
            return {}
        return {
            "id": str(entity.id),
            "exercise_uuid": str(entity.exercise_uuid),
            "version": entity.version,
            "name": entity.name,
            "description": entity.description,
            "primary_muscle_group": self._get_enum_value(entity.primary_muscle_group),
            "secondary_muscle_groups": self._get_enum_list_values(entity.secondary_muscle_groups),
            "movement_pattern": self._get_enum_value(entity.movement_pattern),
            "equipment_required": self._get_enum_list_values(entity.equipment_required),
            "difficulty_level": self._get_enum_value(entity.difficulty_level),
            "is_active": entity.is_active,
            "is_approved": entity.is_approved,
            "approval_status": self._get_enum_value(entity.approval_status),
        }

    async def restore(self, exercise_id: UUID, restored_by: UUID | None = None) -> bool:
        """Restore a soft-deleted exercise."""
        stmt = (
            update(ExerciseModel)
            .where(ExerciseModel.id == exercise_id)
            .values(
                deleted_at=None, updated_by=restored_by, updated_at=datetime.utcnow()
            )
        )

        result = await self.session.execute(stmt)

        if result.rowcount > 0:
            # Get exercise for audit log
            exercise = await self.get_by_id(exercise_id)
            if exercise:
                await self.log_audit(
                    exercise_id=exercise_id,
                    exercise_uuid=exercise.exercise_uuid,
                    action=AuditAction.RESTORED,
                    user_id=restored_by,
                )
            return True

        return False
