"""Exercise API endpoints"""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse

from app.api.v1.schemas.exercise_schemas import (
    ExerciseCreateRequest, ExerciseUpdateRequest, ExerciseSearchRequest,
    ExerciseResponse, ExerciseListResponse, ExerciseSearchResponse,
    ExerciseSummaryResponse
)
from app.api.examples import (
    EXERCISE_CREATE_EXAMPLE, EXERCISE_UPDATE_EXAMPLE, EXERCISE_RESPONSE_EXAMPLE,
    SEARCH_REQUEST_EXAMPLE, SEARCH_RESPONSE_EXAMPLE, RECOMMENDATION_RESPONSE_EXAMPLE,
    ERROR_EXAMPLES
)
from app.application.services.exercise_service import ExerciseService
from app.application.services.exercise_search_service import ExerciseSearchService
from app.domain.exercises.repositories import ExerciseSearchCriteria
from app.domain.exercises.enums import Muscle<PERSON><PERSON>, Equipment, SkillLevel
from app.api.dependencies import get_current_user, get_exercise_service, get_exercise_search_service
from app.application.common.exceptions import EntityNotFoundError, ValidationError, BusinessRuleError
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/exercises", tags=["exercises"])


@router.post(
    "/",
    response_model=ExerciseResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new exercise",
    description="""
    Create a new exercise in the database with comprehensive metadata.

    This endpoint allows authenticated users to create detailed exercise entries
    including instructions, equipment requirements, safety notes, and scientific references.

    **Required fields:**
    - name: Unique exercise name
    - slug: URL-friendly identifier
    - primary_muscle_group: Main muscle group targeted
    - movement_pattern: Type of movement (squat, hinge, push, etc.)
    - exercise_type: Compound, isolation, cardio, etc.
    - skill_level: Beginner, intermediate, advanced, expert
    - difficulty_rating: 1-10 scale
    - equipment_required: List of required equipment
    - short_description: Brief exercise description
    - setup_instructions: Step-by-step setup guide
    - execution_steps: Step-by-step execution guide

    **Optional fields:**
    - Programming guidelines (reps, sets, rest times)
    - Biomechanical data and analysis
    - Media URLs (videos, images)
    - Scientific references
    - Safety notes and contraindications
    """,
    responses={
        201: {
            "description": "Exercise created successfully",
            "content": {
                "application/json": {
                    "example": EXERCISE_RESPONSE_EXAMPLE
                }
            }
        },
        400: {
            "description": "Validation error or business rule violation",
            "content": {
                "application/json": {
                    "example": ERROR_EXAMPLES["validation_error"]
                }
            }
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Not authenticated",
                        "error_code": "AUTHENTICATION_REQUIRED"
                    }
                }
            }
        },
        409: {
            "description": "Exercise with name or slug already exists",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Exercise with slug 'barbell-back-squat' already exists",
                        "error_code": "EXERCISE_ALREADY_EXISTS"
                    }
                }
            }
        }
    },
    openapi_extra={
        "requestBody": {
            "content": {
                "application/json": {
                    "example": EXERCISE_CREATE_EXAMPLE
                }
            }
        }
    }
)
async def create_exercise(
    request: ExerciseCreateRequest,
    exercise_service: ExerciseService = Depends(get_exercise_service),
    current_user = Depends(get_current_user)
) -> ExerciseResponse:
    """Create a new exercise with comprehensive metadata and validation."""
    try:
        exercise = await exercise_service.create_exercise(
            name=request.name,
            slug=request.slug,
            primary_muscle_group=request.primary_muscle_group,
            movement_pattern=request.movement_pattern,
            exercise_type=request.exercise_type,
            skill_level=request.skill_level,
            difficulty_rating=request.difficulty_rating,
            equipment_required=request.equipment_required,
            short_description=request.short_description,
            setup_instructions=request.setup_instructions,
            execution_steps=request.execution_steps,
            created_by=current_user.id,
            **request.dict(exclude={
                'name', 'slug', 'primary_muscle_group', 'movement_pattern',
                'exercise_type', 'skill_level', 'difficulty_rating',
                'equipment_required', 'short_description', 'setup_instructions',
                'execution_steps'
            }, exclude_unset=True)
        )
        
        return _exercise_to_response(exercise)
    
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/", response_model=ExerciseListResponse)
async def list_exercises(
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    muscle_group: Optional[MuscleGroup] = Query(None, description="Filter by muscle group"),
    equipment: Optional[List[Equipment]] = Query(None, description="Filter by equipment"),
    skill_level: Optional[SkillLevel] = Query(None, description="Filter by skill level"),
    is_featured: Optional[bool] = Query(None, description="Filter featured exercises"),
    has_video: Optional[bool] = Query(None, description="Filter exercises with video"),
    exercise_search_service: ExerciseSearchService = Depends(get_exercise_search_service)
) -> ExerciseListResponse:
    """List exercises with optional filters"""
    try:
        result = await exercise_search_service.search_exercises(
            muscle_groups=[muscle_group] if muscle_group else None,
            equipment=equipment,
            skill_levels=[skill_level] if skill_level else None,
            is_featured=is_featured,
            has_video=has_video,
            limit=limit,
            offset=offset
        )
        
        return ExerciseListResponse(
            exercises=[_exercise_to_response(ex) for ex in result.exercises],
            total_count=result.total_count,
            limit=limit,
            offset=offset,
            has_more=result.has_more,
            facets=result.facets
        )
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get(
    "/search",
    response_model=ExerciseSearchResponse,
    summary="Advanced exercise search",
    description="""
    Search exercises with comprehensive filtering and faceted results.

    This endpoint provides powerful search capabilities including:

    **Text Search:**
    - Full-text search across exercise names, descriptions, and instructions
    - Intelligent ranking based on relevance and popularity
    - Autocomplete-friendly partial matching

    **Multi-Dimensional Filtering:**
    - Muscle groups (primary, secondary, tertiary)
    - Equipment requirements (required and optional)
    - Skill levels and difficulty ratings
    - Exercise types (compound, isolation, cardio, etc.)
    - Special categories (bodyweight, gym-only, with video)

    **Advanced Features:**
    - Faceted search results with counts
    - Flexible sorting options
    - Pagination with has_more indicator
    - Applied filters tracking

    **Use Cases:**
    - "Find chest exercises with dumbbells for beginners"
    - "Show me bodyweight exercises with video demonstrations"
    - "Advanced compound movements for powerlifting"
    """,
    responses={
        200: {
            "description": "Search completed successfully",
            "content": {
                "application/json": {
                    "example": SEARCH_RESPONSE_EXAMPLE
                }
            }
        },
        400: {
            "description": "Invalid search parameters",
            "content": {
                "application/json": {
                    "example": ERROR_EXAMPLES["validation_error"]
                }
            }
        }
    }
)
async def search_exercises(
    q: Optional[str] = Query(
        None,
        description="Search query - searches names, descriptions, and instructions",
        example="chest press dumbbell"
    ),
    muscle_groups: Optional[List[MuscleGroup]] = Query(
        None,
        description="Filter by target muscle groups",
        example=["chest", "shoulders"]
    ),
    equipment: Optional[List[Equipment]] = Query(
        None,
        description="Filter by available equipment",
        example=["dumbbell", "bench"]
    ),
    skill_levels: Optional[List[SkillLevel]] = Query(
        None,
        description="Filter by appropriate skill levels",
        example=["beginner", "intermediate"]
    ),
    difficulty_min: Optional[int] = Query(
        None,
        ge=1,
        le=10,
        description="Minimum difficulty rating (1-10)",
        example=1
    ),
    difficulty_max: Optional[int] = Query(
        None,
        ge=1,
        le=10,
        description="Maximum difficulty rating (1-10)",
        example=6
    ),
    is_bodyweight: Optional[bool] = Query(
        None,
        description="Filter for bodyweight-only exercises",
        example=False
    ),
    requires_gym: Optional[bool] = Query(
        None,
        description="Filter for gym-based exercises",
        example=False
    ),
    has_video: Optional[bool] = Query(
        None,
        description="Filter for exercises with video demonstrations",
        example=True
    ),
    limit: int = Query(
        20,
        ge=1,
        le=100,
        description="Maximum number of results per page",
        example=20
    ),
    offset: int = Query(
        0,
        ge=0,
        description="Number of results to skip for pagination",
        example=0
    ),
    sort_by: str = Query(
        "popularity_score",
        description="Field to sort by",
        example="popularity_score"
    ),
    sort_order: str = Query(
        "desc",
        regex="^(asc|desc)$",
        description="Sort order (asc or desc)",
        example="desc"
    ),
    exercise_search_service: ExerciseSearchService = Depends(get_exercise_search_service)
) -> ExerciseSearchResponse:
    """Search exercises with advanced multi-dimensional filtering and faceted results."""
    try:
        result = await exercise_search_service.search_exercises(
            query=q,
            muscle_groups=muscle_groups,
            equipment=equipment,
            skill_levels=skill_levels,
            difficulty_min=difficulty_min,
            difficulty_max=difficulty_max,
            is_bodyweight=is_bodyweight,
            requires_gym=requires_gym,
            has_video=has_video,
            limit=limit,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        filters_applied = {
            "query": q,
            "muscle_groups": muscle_groups,
            "equipment": equipment,
            "skill_levels": skill_levels,
            "difficulty_range": [difficulty_min, difficulty_max] if difficulty_min or difficulty_max else None,
            "is_bodyweight": is_bodyweight,
            "requires_gym": requires_gym,
            "has_video": has_video
        }
        
        return ExerciseSearchResponse(
            exercises=[_exercise_to_response(ex) for ex in result.exercises],
            total_count=result.total_count,
            limit=limit,
            offset=offset,
            has_more=result.has_more,
            facets=result.facets,
            query=q,
            filters_applied=filters_applied
        )
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/featured", response_model=List[ExerciseSummaryResponse])
async def get_featured_exercises(
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> List[ExerciseSummaryResponse]:
    """Get featured exercises"""
    try:
        exercises = await exercise_service.get_featured_exercises(limit)
        return [_exercise_to_summary_response(ex) for ex in exercises]
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/popular", response_model=List[ExerciseSummaryResponse])
async def get_popular_exercises(
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> List[ExerciseSummaryResponse]:
    """Get popular exercises"""
    try:
        exercises = await exercise_service.get_popular_exercises(limit)
        return [_exercise_to_summary_response(ex) for ex in exercises]
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/recent", response_model=List[ExerciseSummaryResponse])
async def get_recent_exercises(
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> List[ExerciseSummaryResponse]:
    """Get recently added exercises"""
    try:
        exercises = await exercise_service.get_recent_exercises(limit)
        return [_exercise_to_summary_response(ex) for ex in exercises]
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/muscle-groups/{muscle_group}", response_model=List[ExerciseSummaryResponse])
async def get_exercises_by_muscle_group(
    muscle_group: MuscleGroup,
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> List[ExerciseSummaryResponse]:
    """Get exercises by muscle group"""
    try:
        exercises = await exercise_service.get_exercises_by_muscle_group(
            muscle_group, limit, offset
        )
        return [_exercise_to_summary_response(ex) for ex in exercises]
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/recommendations", response_model=List[ExerciseSummaryResponse])
async def get_exercise_recommendations(
    muscle_groups: List[MuscleGroup] = Query(..., description="Target muscle groups"),
    equipment: List[Equipment] = Query(..., description="Available equipment"),
    skill_level: SkillLevel = Query(..., description="User skill level"),
    workout_type: str = Query("strength", description="Workout type"),
    limit: int = Query(10, ge=1, le=20, description="Maximum number of results"),
    exercise_search_service: ExerciseSearchService = Depends(get_exercise_search_service)
) -> List[ExerciseSummaryResponse]:
    """Get personalized exercise recommendations"""
    try:
        exercises = await exercise_search_service.get_exercise_recommendations(
            user_equipment=equipment,
            user_skill_level=skill_level,
            target_muscle_groups=muscle_groups,
            workout_type=workout_type,
            limit=limit
        )
        return [_exercise_to_summary_response(ex) for ex in exercises]
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/{exercise_id}", response_model=ExerciseResponse)
async def get_exercise(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseResponse:
    """Get exercise by ID"""
    try:
        exercise = await exercise_service.get_exercise_by_id(exercise_id)
        return _exercise_to_response(exercise)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/slug/{slug}", response_model=ExerciseResponse)
async def get_exercise_by_slug(
    slug: str,
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseResponse:
    """Get exercise by slug"""
    try:
        exercise = await exercise_service.get_exercise_by_slug(slug)
        return _exercise_to_response(exercise)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.put("/{exercise_id}", response_model=ExerciseResponse)
async def update_exercise(
    exercise_id: UUID,
    request: ExerciseUpdateRequest,
    exercise_service: ExerciseService = Depends(get_exercise_service),
    current_user = Depends(get_current_user)
) -> ExerciseResponse:
    """Update an exercise"""
    try:
        exercise = await exercise_service.update_exercise(
            exercise_id=exercise_id,
            updated_by=current_user.id,
            **request.dict(exclude_unset=True)
        )
        return _exercise_to_response(exercise)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post("/{exercise_id}/submit-for-review", response_model=ExerciseResponse)
async def submit_exercise_for_review(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service),
    current_user = Depends(get_current_user)
) -> ExerciseResponse:
    """Submit exercise for review"""
    try:
        exercise = await exercise_service.submit_exercise_for_review(
            exercise_id, current_user.id
        )
        return _exercise_to_response(exercise)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except BusinessRuleError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post("/{exercise_id}/approve", response_model=ExerciseResponse)
async def approve_exercise(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service),
    current_user = Depends(get_current_user)
) -> ExerciseResponse:
    """Approve exercise (admin only)"""
    try:
        exercise = await exercise_service.approve_exercise(exercise_id, current_user.id)
        return _exercise_to_response(exercise)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except BusinessRuleError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post("/{exercise_id}/publish", response_model=ExerciseResponse)
async def publish_exercise(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service),
    current_user = Depends(get_current_user)
) -> ExerciseResponse:
    """Publish exercise (admin only)"""
    try:
        exercise = await exercise_service.publish_exercise(exercise_id, current_user.id)
        return _exercise_to_response(exercise)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except BusinessRuleError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.delete("/{exercise_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_exercise(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service),
    current_user = Depends(get_current_user)
):
    """Delete exercise (soft delete)"""
    try:
        await exercise_service.delete_exercise(exercise_id)
    
    except EntityNotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exercise not found")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Helper functions
def _exercise_to_response(exercise) -> ExerciseResponse:
    """Convert exercise entity to response DTO"""
    return ExerciseResponse(
        id=exercise.id,
        name=exercise.identification.name,
        slug=exercise.identification.slug,
        alternative_names=exercise.identification.alternative_names,
        primary_muscle_group=exercise.classification.primary_muscle_group,
        secondary_muscle_groups=exercise.classification.secondary_muscle_groups,
        tertiary_muscle_groups=exercise.classification.tertiary_muscle_groups,
        movement_pattern=exercise.classification.movement_pattern,
        exercise_type=exercise.classification.exercise_type,
        skill_level=exercise.requirements.skill_level,
        difficulty_rating=exercise.requirements.difficulty_rating,
        equipment_required=exercise.requirements.equipment_required,
        equipment_optional=exercise.requirements.equipment_optional,
        space_requirement=exercise.requirements.space_requirement,
        short_description=exercise.instructions.short_description,
        detailed_description=exercise.instructions.detailed_description,
        setup_instructions=exercise.instructions.setup_instructions,
        execution_steps=exercise.instructions.execution_steps,
        breathing_pattern=exercise.instructions.breathing_pattern,
        form_cues=exercise.instructions.form_cues,
        common_mistakes=exercise.instructions.common_mistakes,
        safety_notes=exercise.instructions.safety_notes,
        contraindications=exercise.instructions.contraindications,
        injury_modifications=exercise.instructions.injury_modifications,
        rep_range_min=exercise.programming_guidelines.rep_range_min if exercise.programming_guidelines else None,
        rep_range_max=exercise.programming_guidelines.rep_range_max if exercise.programming_guidelines else None,
        typical_sets=exercise.programming_guidelines.typical_sets if exercise.programming_guidelines else None,
        rest_time_min_seconds=exercise.programming_guidelines.rest_time_min_seconds if exercise.programming_guidelines else None,
        rest_time_max_seconds=exercise.programming_guidelines.rest_time_max_seconds if exercise.programming_guidelines else None,
        tempo_recommendation=exercise.programming_guidelines.tempo_recommendation if exercise.programming_guidelines else None,
        primary_video_url=exercise.media.primary_video_url if exercise.media else None,
        primary_image_url=exercise.media.primary_image_url if exercise.media else None,
        thumbnail_url=exercise.media.thumbnail_url if exercise.media else None,
        demonstration_angles=exercise.media.demonstration_angles if exercise.media else [],
        has_video=exercise.has_media and exercise.media.has_video,
        has_images=exercise.has_media and exercise.media.has_images,
        popularity_score=exercise.metrics.popularity_score if exercise.metrics else None,
        quality_score=exercise.metrics.quality_score if exercise.metrics else None,
        user_rating=exercise.metrics.user_rating if exercise.metrics else None,
        total_ratings=exercise.metrics.total_ratings if exercise.metrics else 0,
        content_status=exercise.content_management.content_status,
        review_status=exercise.content_management.review_status,
        is_active=exercise.content_management.is_active,
        is_featured=exercise.content_management.is_featured,
        is_premium=exercise.content_management.is_premium,
        version=exercise.content_management.version,
        tags=exercise.content_management.tags,
        categories=exercise.content_management.categories,
        training_goals=exercise.content_management.training_goals,
        scientific_references=exercise.scientific_references,
        has_scientific_backing=exercise.has_scientific_backing,
        parent_exercise_id=exercise.parent_exercise_id,
        created_by=exercise.created_by,
        created_at=exercise.created_at,
        updated_at=exercise.updated_at
    )


def _exercise_to_summary_response(exercise) -> ExerciseSummaryResponse:
    """Convert exercise entity to summary response DTO"""
    return ExerciseSummaryResponse(
        id=exercise.id,
        name=exercise.identification.name,
        slug=exercise.identification.slug,
        primary_muscle_group=exercise.classification.primary_muscle_group,
        exercise_type=exercise.classification.exercise_type,
        skill_level=exercise.requirements.skill_level,
        difficulty_rating=exercise.requirements.difficulty_rating,
        equipment_required=exercise.requirements.equipment_required,
        short_description=exercise.instructions.short_description,
        thumbnail_url=exercise.media.thumbnail_url if exercise.media else None,
        has_video=exercise.has_media and exercise.media.has_video,
        popularity_score=exercise.metrics.popularity_score if exercise.metrics else None,
        is_featured=exercise.content_management.is_featured
    )
