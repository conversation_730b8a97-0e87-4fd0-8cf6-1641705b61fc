"""Tests for Auth API routes.

Comprehensive test suite for authentication endpoints including registration,
login, profile management, and error handling.
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4
from datetime import datetime
from fastapi import HTTPException, status

from app.application.exceptions import (
    BusinessRuleViolationError,
    NotFoundError,
    ValidationError,
)
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import (
    InvalidCredentialsError,
    UserAlreadyExistsError,
)
from app.presentation.schemas.auth_schema import (
    LoginRequest,
    LoginResponse,
)
from app.presentation.schemas.user_schema import (
    UserCreateRequest,
    UserResponse,
    UserUpdateRequest,
)


class TestAuthRoutes:
    """Test class for authentication API routes."""

    @pytest.fixture
    def mock_register_use_case(self):
        """Create mock register use case."""
        return AsyncMock()

    @pytest.fixture
    def mock_authenticate_use_case(self):
        """Create mock authenticate use case."""
        return AsyncMock()

    @pytest.fixture
    def mock_get_user_profile_use_case(self):
        """Create mock get user profile use case."""
        return AsyncMock()

    @pytest.fixture
    def mock_update_user_profile_use_case(self):
        """Create mock update user profile use case."""
        return AsyncMock()

    @pytest.fixture
    def mock_current_user(self):
        """Create mock current user."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            is_active=True,
            is_verified=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

    @pytest.fixture
    def register_request(self):
        """Create register request."""
        return UserCreateRequest(
            email="<EMAIL>",
            password="SecurePassword123!",
            first_name="New",
            last_name="User",
        )

    @pytest.fixture
    def login_request(self):
        """Create login request."""
        return LoginRequest(
            email="<EMAIL>",
            password="SecurePassword123!",
        )

    @pytest.fixture
    def user_update_request(self):
        """Create user update request."""
        return UserUpdateRequest(
            first_name="Updated",
            last_name="Name",
            bio="Updated bio",
        )

    # Register Tests

    async def test_register_success(
        self, mock_register_use_case, register_request
    ):
        """Test successful user registration."""
        from app.presentation.api.v1.auth import register

        # Mock successful registration response
        mock_response = Mock()
        mock_response.user = Mock()
        mock_response.access_token = "access_token_123"
        mock_response.refresh_token = "refresh_token_123"
        mock_response.token_type = "bearer"
        mock_register_use_case.execute.return_value = mock_response

        result = await register(
            request=register_request,
            use_case=mock_register_use_case,
        )

        # Verify use case was called correctly
        mock_register_use_case.execute.assert_called_once()

        # Verify response
        assert isinstance(result, LoginResponse)
        assert result.access_token == "access_token_123"
        assert result.refresh_token == "refresh_token_123"
        assert result.token_type == "bearer"

    async def test_register_user_already_exists(
        self, mock_register_use_case, register_request
    ):
        """Test registration when user already exists."""
        from app.presentation.api.v1.auth import register

        # Mock user already exists error
        mock_register_use_case.execute.side_effect = UserAlreadyExistsError("User already exists")

        with pytest.raises(HTTPException) as exc_info:
            await register(
                request=register_request,
                use_case=mock_register_use_case,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "User already exists" in str(exc_info.value.detail)

    async def test_register_validation_error(
        self, mock_register_use_case, register_request
    ):
        """Test registration with validation error."""
        from app.presentation.api.v1.auth import register
        from app.domain.exceptions.auth_exceptions import WeakPasswordError

        # Mock validation error
        mock_register_use_case.execute.side_effect = WeakPasswordError("Password too weak")

        with pytest.raises(HTTPException) as exc_info:
            await register(
                request=register_request,
                use_case=mock_register_use_case,
            )

        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert "Password too weak" in str(exc_info.value.detail)

    # Login Tests

    async def test_login_success(
        self, mock_authenticate_use_case, login_request
    ):
        """Test successful user login."""
        from app.presentation.api.v1.auth import login

        # Mock successful authentication response
        mock_response = Mock()
        mock_response.user = Mock()
        mock_response.access_token = "access_token_456"
        mock_response.refresh_token = "refresh_token_456"
        mock_response.token_type = "bearer"
        mock_authenticate_use_case.execute.return_value = mock_response

        result = await login(
            request=login_request,
            use_case=mock_authenticate_use_case,
        )

        # Verify use case was called correctly
        mock_authenticate_use_case.execute.assert_called_once()

        # Verify response
        assert isinstance(result, LoginResponse)
        assert result.access_token == "access_token_456"
        assert result.refresh_token == "refresh_token_456"
        assert result.token_type == "bearer"

    async def test_login_invalid_credentials(
        self, mock_authenticate_use_case, login_request
    ):
        """Test login with invalid credentials."""
        from app.presentation.api.v1.auth import login

        # Mock invalid credentials error
        mock_authenticate_use_case.execute.side_effect = InvalidCredentialsError("Invalid credentials")

        with pytest.raises(HTTPException) as exc_info:
            await login(
                request=login_request,
                use_case=mock_authenticate_use_case,
            )

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid credentials" in str(exc_info.value.detail)

    async def test_login_inactive_user(
        self, mock_authenticate_use_case, login_request
    ):
        """Test login with inactive user."""
        from app.presentation.api.v1.auth import login
        from app.domain.exceptions.auth_exceptions import InactiveUserError

        # Mock inactive user error
        mock_authenticate_use_case.execute.side_effect = InactiveUserError("User is inactive")

        with pytest.raises(HTTPException) as exc_info:
            await login(
                request=login_request,
                use_case=mock_authenticate_use_case,
            )

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "User is inactive" in str(exc_info.value.detail)

    # Get Profile Tests

    async def test_get_profile_success(
        self, mock_get_user_profile_use_case, mock_current_user
    ):
        """Test successful current user profile retrieval."""
        from app.presentation.api.v1.auth import get_profile

        # Mock successful profile retrieval
        mock_user_dto = Mock()
        mock_user_dto.id = mock_current_user.id
        mock_user_dto.email = mock_current_user.email
        mock_get_user_profile_use_case.execute.return_value = mock_user_dto

        result = await get_profile(
            current_user_id=mock_current_user.id,
            use_case=mock_get_user_profile_use_case,
        )

        # Verify use case was called correctly
        mock_get_user_profile_use_case.execute.assert_called_once_with(mock_current_user.id)

        # Verify response
        assert isinstance(result, UserResponse)

    async def test_get_profile_not_found(
        self, mock_get_user_profile_use_case, mock_current_user
    ):
        """Test current user profile retrieval when user not found."""
        from app.presentation.api.v1.auth import get_profile
        from app.domain.exceptions.auth_exceptions import UserNotFoundError

        # Mock user not found error
        mock_get_user_profile_use_case.execute.side_effect = UserNotFoundError("User not found")

        with pytest.raises(HTTPException) as exc_info:
            await get_profile(
                current_user_id=mock_current_user.id,
                use_case=mock_get_user_profile_use_case,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found" in str(exc_info.value.detail)

    # Update Profile Tests

    async def test_update_profile_success(
        self, mock_update_user_profile_use_case, mock_current_user, user_update_request
    ):
        """Test successful current user profile update."""
        from app.presentation.api.v1.auth import update_profile

        # Mock successful profile update
        mock_user_dto = Mock()
        mock_user_dto.id = mock_current_user.id
        mock_user_dto.email = mock_current_user.email
        mock_user_dto.first_name = user_update_request.first_name
        mock_user_dto.last_name = user_update_request.last_name
        mock_update_user_profile_use_case.execute.return_value = mock_user_dto

        result = await update_profile(
            request=user_update_request,
            current_user_id=mock_current_user.id,
            use_case=mock_update_user_profile_use_case,
        )

        # Verify use case was called correctly
        mock_update_user_profile_use_case.execute.assert_called_once()

        # Verify response
        assert isinstance(result, UserResponse)

    async def test_update_profile_not_found(
        self, mock_update_user_profile_use_case, mock_current_user, user_update_request
    ):
        """Test current user profile update when user not found."""
        from app.presentation.api.v1.auth import update_profile
        from app.domain.exceptions.auth_exceptions import UserNotFoundError

        # Mock user not found error
        mock_update_user_profile_use_case.execute.side_effect = UserNotFoundError("User not found")

        with pytest.raises(HTTPException) as exc_info:
            await update_profile(
                request=user_update_request,
                current_user_id=mock_current_user.id,
                use_case=mock_update_user_profile_use_case,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found" in str(exc_info.value.detail)
