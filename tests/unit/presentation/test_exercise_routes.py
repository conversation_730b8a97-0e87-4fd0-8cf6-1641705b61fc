"""Tests for Exercise API routes.

Comprehensive test suite for exercise endpoints including CRUD operations,
authentication, authorization, error handling, and edge cases.
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4
from datetime import datetime
from fastapi import HTTPException, status
from fastapi.testclient import TestClient

from app.application.exceptions import (
    BusinessRuleViolationError,
    NotFoundError,
    ValidationError,
)
from app.domain.entities.exercise import (
    Exercise,
    ExerciseMedia,
    MediaType,
    MuscleGroup,
    MovementPattern,
    Equipment,
    DifficultyLevel,
    ApprovalStatus,
    ChangeReason,
)
from app.presentation.api.v1.routes.exercises import router, get_exercise_service
from app.presentation.schemas.exercise_schemas import (
    ExerciseCreateRequest,
    ExerciseUpdateRequest,
    ExerciseApprovalRequest,
    ExerciseRejectionRequest,
    ExerciseMediaCreateRequest,
    ExerciseMediaUpdateRequest,
)


class TestExerciseRoutes:
    """Test class for exercise API routes."""

    @pytest.fixture
    def mock_exercise_service(self):
        """Create mock exercise service."""
        return AsyncMock()

    @pytest.fixture
    def mock_current_user(self):
        """Create mock current user."""
        user = Mock()
        user.id = uuid4()
        user.email = "<EMAIL>"
        return user

    @pytest.fixture
    def sample_exercise(self):
        """Create sample exercise entity."""
        now = datetime.utcnow()
        return Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Test Exercise",
            description="Test description",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            is_active=True,
            is_approved=True,
            approval_status=ApprovalStatus.APPROVED,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )

    @pytest.fixture
    def sample_exercise_media(self):
        """Create sample exercise media entity."""
        now = datetime.utcnow()
        return ExerciseMedia(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            title="Test Video",
            description="Test video description",
            sort_order=1,
            is_primary=True,
            is_active=True,
            created_at=now,
            updated_at=now,
        )

    @pytest.fixture
    def exercise_create_request(self):
        """Create exercise create request."""
        return ExerciseCreateRequest(
            name="New Exercise",
            description="New exercise description",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            form_cues=["Keep chest up", "Control the weight"],
            setup_instructions="Set up the barbell",
            execution_steps=["Step 1", "Step 2"],
            common_mistakes=["Don't bounce", "Don't arch too much"],
            safety_notes="Use a spotter",
            version_notes="Initial version",
        )

    # Create Exercise Tests

    async def test_create_exercise_success(
        self, mock_exercise_service, mock_current_user, sample_exercise, exercise_create_request
    ):
        """Test successful exercise creation."""
        from app.presentation.api.v1.routes.exercises import create_exercise

        # Mock service method
        mock_exercise_service.create_exercise.return_value = sample_exercise

        # Call endpoint
        result = await create_exercise(
            request=exercise_create_request,
            current_user=mock_current_user,
            exercise_service=mock_exercise_service,
        )

        # Verify service was called correctly
        mock_exercise_service.create_exercise.assert_called_once()
        call_args = mock_exercise_service.create_exercise.call_args[0]
        domain_request = call_args[0]
        user_id = call_args[1]

        assert domain_request.name == exercise_create_request.name
        assert domain_request.description == exercise_create_request.description
        assert user_id == mock_current_user.id

        # Verify response
        assert result.name == sample_exercise.name
        assert result.description == sample_exercise.description

    async def test_create_exercise_validation_error(
        self, mock_exercise_service, mock_current_user, exercise_create_request
    ):
        """Test exercise creation with validation error."""
        from app.presentation.api.v1.routes.exercises import create_exercise

        # Mock service to raise validation error
        mock_exercise_service.create_exercise.side_effect = ValidationError("Invalid data")

        # Call endpoint and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await create_exercise(
                request=exercise_create_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Validation failed" in str(exc_info.value.detail)

    async def test_create_exercise_business_rule_violation(
        self, mock_exercise_service, mock_current_user, exercise_create_request
    ):
        """Test exercise creation with business rule violation."""
        from app.presentation.api.v1.routes.exercises import create_exercise

        # Mock service to raise business rule violation
        mock_exercise_service.create_exercise.side_effect = BusinessRuleViolationError(
            "Exercise name already exists"
        )

        # Call endpoint and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await create_exercise(
                request=exercise_create_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "Business rule violation" in str(exc_info.value.detail)

    # Get Exercise Tests

    async def test_get_exercise_success(self, mock_exercise_service, sample_exercise):
        """Test successful exercise retrieval."""
        from app.presentation.api.v1.routes.exercises import get_exercise

        exercise_id = sample_exercise.id
        mock_exercise_service.get_exercise_by_id.return_value = sample_exercise

        result = await get_exercise(
            exercise_id=exercise_id,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.get_exercise_by_id.assert_called_once_with(exercise_id)
        assert result.id == sample_exercise.id
        assert result.name == sample_exercise.name

    async def test_get_exercise_not_found(self, mock_exercise_service):
        """Test exercise retrieval when not found."""
        from app.presentation.api.v1.routes.exercises import get_exercise

        exercise_id = uuid4()
        mock_exercise_service.get_exercise_by_id.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await get_exercise(
                exercise_id=exercise_id,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Exercise not found" in str(exc_info.value.detail)

    # Update Exercise Tests

    async def test_update_exercise_success(
        self, mock_exercise_service, mock_current_user, sample_exercise
    ):
        """Test successful exercise update."""
        from app.presentation.api.v1.routes.exercises import update_exercise

        exercise_id = sample_exercise.id
        update_request = ExerciseUpdateRequest(
            name="Updated Exercise",
            description="Updated description",
            change_reason=ChangeReason.CONTENT_UPDATE,
        )

        mock_exercise_service.update_exercise.return_value = sample_exercise

        result = await update_exercise(
            exercise_id=exercise_id,
            request=update_request,
            current_user=mock_current_user,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.update_exercise.assert_called_once()
        assert result.id == sample_exercise.id

    async def test_update_exercise_not_found(
        self, mock_exercise_service, mock_current_user
    ):
        """Test exercise update when not found."""
        from app.presentation.api.v1.routes.exercises import update_exercise

        exercise_id = uuid4()
        update_request = ExerciseUpdateRequest(name="Updated Exercise")

        mock_exercise_service.update_exercise.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await update_exercise(
                exercise_id=exercise_id,
                request=update_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    # Delete Exercise Tests

    async def test_delete_exercise_success(self, mock_exercise_service, mock_current_user):
        """Test successful exercise deletion."""
        from app.presentation.api.v1.routes.exercises import delete_exercise

        exercise_id = uuid4()
        mock_exercise_service.delete_exercise.return_value = None

        result = await delete_exercise(
            exercise_id=exercise_id,
            current_user=mock_current_user,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.delete_exercise.assert_called_once_with(exercise_id, mock_current_user.id)
        assert result is None

    async def test_delete_exercise_not_found(self, mock_exercise_service, mock_current_user):
        """Test exercise deletion when not found."""
        from app.presentation.api.v1.routes.exercises import delete_exercise

        exercise_id = uuid4()
        mock_exercise_service.delete_exercise.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await delete_exercise(
                exercise_id=exercise_id,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    # Restore Exercise Tests

    async def test_restore_exercise_success(self, mock_exercise_service, mock_current_user, sample_exercise):
        """Test successful exercise restoration."""
        from app.presentation.api.v1.routes.exercises import restore_exercise

        exercise_id = sample_exercise.id
        mock_exercise_service.restore_exercise.return_value = sample_exercise

        result = await restore_exercise(
            exercise_id=exercise_id,
            current_user=mock_current_user,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.restore_exercise.assert_called_once_with(exercise_id, mock_current_user.id)
        assert result.id == sample_exercise.id

    async def test_restore_exercise_not_found(self, mock_exercise_service, mock_current_user):
        """Test exercise restoration when not found."""
        from app.presentation.api.v1.routes.exercises import restore_exercise

        exercise_id = uuid4()
        mock_exercise_service.restore_exercise.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await restore_exercise(
                exercise_id=exercise_id,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    # Approve Exercise Tests

    async def test_approve_exercise_success(self, mock_exercise_service, mock_current_user, sample_exercise):
        """Test successful exercise approval."""
        from app.presentation.api.v1.routes.exercises import approve_exercise

        exercise_id = sample_exercise.id
        approval_request = ExerciseApprovalRequest(notes="Looks good")
        mock_exercise_service.approve_exercise.return_value = sample_exercise

        result = await approve_exercise(
            exercise_id=exercise_id,
            request=approval_request,
            current_user=mock_current_user,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.approve_exercise.assert_called_once_with(
            exercise_id, mock_current_user.id, approval_request.notes
        )
        assert result.id == sample_exercise.id

    async def test_approve_exercise_not_found(self, mock_exercise_service, mock_current_user):
        """Test exercise approval when not found."""
        from app.presentation.api.v1.routes.exercises import approve_exercise

        exercise_id = uuid4()
        approval_request = ExerciseApprovalRequest(notes="Looks good")
        mock_exercise_service.approve_exercise.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await approve_exercise(
                exercise_id=exercise_id,
                request=approval_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    async def test_approve_exercise_business_rule_violation(self, mock_exercise_service, mock_current_user):
        """Test exercise approval with business rule violation."""
        from app.presentation.api.v1.routes.exercises import approve_exercise

        exercise_id = uuid4()
        approval_request = ExerciseApprovalRequest(notes="Looks good")
        mock_exercise_service.approve_exercise.side_effect = BusinessRuleViolationError(
            "Exercise already approved"
        )

        with pytest.raises(HTTPException) as exc_info:
            await approve_exercise(
                exercise_id=exercise_id,
                request=approval_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    # Reject Exercise Tests

    async def test_reject_exercise_success(self, mock_exercise_service, mock_current_user, sample_exercise):
        """Test successful exercise rejection."""
        from app.presentation.api.v1.routes.exercises import reject_exercise

        exercise_id = sample_exercise.id
        rejection_request = ExerciseRejectionRequest(notes="Needs improvement")
        mock_exercise_service.reject_exercise.return_value = sample_exercise

        result = await reject_exercise(
            exercise_id=exercise_id,
            request=rejection_request,
            current_user=mock_current_user,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.reject_exercise.assert_called_once_with(
            exercise_id, mock_current_user.id, rejection_request.notes
        )
        assert result.id == sample_exercise.id

    async def test_reject_exercise_not_found(self, mock_exercise_service, mock_current_user):
        """Test exercise rejection when not found."""
        from app.presentation.api.v1.routes.exercises import reject_exercise

        exercise_id = uuid4()
        rejection_request = ExerciseRejectionRequest(notes="Needs improvement")
        mock_exercise_service.reject_exercise.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await reject_exercise(
                exercise_id=exercise_id,
                request=rejection_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    async def test_reject_exercise_business_rule_violation(self, mock_exercise_service, mock_current_user):
        """Test exercise rejection with business rule violation."""
        from app.presentation.api.v1.routes.exercises import reject_exercise

        exercise_id = uuid4()
        rejection_request = ExerciseRejectionRequest(notes="Needs improvement")
        mock_exercise_service.reject_exercise.side_effect = BusinessRuleViolationError(
            "Exercise cannot be rejected"
        )

        with pytest.raises(HTTPException) as exc_info:
            await reject_exercise(
                exercise_id=exercise_id,
                request=rejection_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    # Exercise Media Tests

    async def test_add_exercise_media_success(self, mock_exercise_service, sample_exercise_media):
        """Test successful exercise media addition."""
        from app.presentation.api.v1.routes.exercises import add_exercise_media

        exercise_id = uuid4()
        media_request = ExerciseMediaCreateRequest(
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            title="Test Video",
            description="Test video description",
            is_primary=True,
            sort_order=1,
        )

        mock_exercise_service.add_exercise_media.return_value = sample_exercise_media

        result = await add_exercise_media(
            exercise_id=exercise_id,
            request=media_request,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.add_exercise_media.assert_called_once_with(
            exercise_id=exercise_id,
            media_type=media_request.media_type,
            url=media_request.url,
            title=media_request.title,
            description=media_request.description,
            thumbnail_url=media_request.thumbnail_url,
            file_size_bytes=media_request.file_size_bytes,
            duration_seconds=media_request.duration_seconds,
            width_pixels=media_request.width_pixels,
            height_pixels=media_request.height_pixels,
            mime_type=media_request.mime_type,
            is_primary=media_request.is_primary,
            sort_order=media_request.sort_order,
        )
        assert result.id == sample_exercise_media.id

    async def test_add_exercise_media_exercise_not_found(self, mock_exercise_service):
        """Test adding media when exercise not found."""
        from app.presentation.api.v1.routes.exercises import add_exercise_media

        exercise_id = uuid4()
        media_request = ExerciseMediaCreateRequest(
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            title="Test Video",
        )

        mock_exercise_service.add_exercise_media.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await add_exercise_media(
                exercise_id=exercise_id,
                request=media_request,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    async def test_add_exercise_media_validation_error(self, mock_exercise_service):
        """Test adding media with validation error."""
        from app.presentation.api.v1.routes.exercises import add_exercise_media

        exercise_id = uuid4()
        media_request = ExerciseMediaCreateRequest(
            media_type=MediaType.VIDEO,
            url="invalid-url",
            title="Test Video",
        )

        mock_exercise_service.add_exercise_media.side_effect = ValidationError("Invalid URL")

        with pytest.raises(HTTPException) as exc_info:
            await add_exercise_media(
                exercise_id=exercise_id,
                request=media_request,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    async def test_add_exercise_media_business_rule_violation(self, mock_exercise_service):
        """Test adding media with business rule violation."""
        from app.presentation.api.v1.routes.exercises import add_exercise_media

        exercise_id = uuid4()
        media_request = ExerciseMediaCreateRequest(
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            title="Test Video",
        )

        mock_exercise_service.add_exercise_media.side_effect = BusinessRuleViolationError(
            "Primary media already exists"
        )

        with pytest.raises(HTTPException) as exc_info:
            await add_exercise_media(
                exercise_id=exercise_id,
                request=media_request,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT

    async def test_get_exercise_media_success(self, mock_exercise_service, sample_exercise_media):
        """Test successful exercise media retrieval."""
        from app.presentation.api.v1.routes.exercises import get_exercise_media

        exercise_id = uuid4()
        mock_exercise_service.get_exercise_media.return_value = [sample_exercise_media]

        result = await get_exercise_media(
            exercise_id=exercise_id,
            media_type=None,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.get_exercise_media.assert_called_once_with(exercise_id)
        assert len(result) == 1
        assert result[0].id == sample_exercise_media.id

    async def test_get_exercise_media_with_type_filter(self, mock_exercise_service, sample_exercise_media):
        """Test exercise media retrieval with type filter."""
        from app.presentation.api.v1.routes.exercises import get_exercise_media

        exercise_id = uuid4()
        media_type = MediaType.VIDEO
        mock_exercise_service.get_media_by_type.return_value = [sample_exercise_media]

        result = await get_exercise_media(
            exercise_id=exercise_id,
            media_type=media_type,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.get_media_by_type.assert_called_once_with(exercise_id, media_type)
        assert len(result) == 1
        assert result[0].id == sample_exercise_media.id

    async def test_get_exercise_media_exercise_not_found(self, mock_exercise_service):
        """Test getting media when exercise not found."""
        from app.presentation.api.v1.routes.exercises import get_exercise_media

        exercise_id = uuid4()
        mock_exercise_service.get_exercise_media.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await get_exercise_media(
                exercise_id=exercise_id,
                media_type=None,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    # Search Exercises Tests

    async def test_search_exercises_success(self, mock_exercise_service, sample_exercise):
        """Test successful exercise search."""
        from app.presentation.api.v1.routes.exercises import search_exercises

        # Mock search results
        mock_exercise_service.search_exercises.return_value = [sample_exercise]
        mock_exercise_service.count_exercises.return_value = 1

        result = await search_exercises(
            name="test",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            equipment_required=Equipment.BARBELL,
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            is_approved=True,
            limit=10,
            offset=0,
            exercise_service=mock_exercise_service,
        )

        # Verify service calls
        mock_exercise_service.search_exercises.assert_called_once()
        mock_exercise_service.count_exercises.assert_called_once()

        # Verify response structure
        assert result.exercises is not None
        assert len(result.exercises) == 1
        assert result.exercises[0].id == sample_exercise.id
        assert result.total == 1
        assert result.limit == 10
        assert result.offset == 0

    async def test_search_exercises_empty_results(self, mock_exercise_service):
        """Test exercise search with no results."""
        from app.presentation.api.v1.routes.exercises import search_exercises

        # Mock empty search results
        mock_exercise_service.search_exercises.return_value = []
        mock_exercise_service.count_exercises.return_value = 0

        result = await search_exercises(
            name="nonexistent",
            limit=10,
            offset=0,
            exercise_service=mock_exercise_service,
        )

        assert result.exercises == []
        assert result.total == 0

    async def test_search_exercises_with_all_filters(self, mock_exercise_service, sample_exercise):
        """Test exercise search with all possible filters."""
        from app.presentation.api.v1.routes.exercises import search_exercises

        mock_exercise_service.search_exercises.return_value = [sample_exercise]
        mock_exercise_service.count_exercises.return_value = 1

        result = await search_exercises(
            name="test",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            equipment_required=Equipment.BARBELL,
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            is_approved=True,
            limit=20,
            offset=10,
            exercise_service=mock_exercise_service,
        )

        # Verify filters were passed correctly
        search_call_args = mock_exercise_service.search_exercises.call_args[1]
        assert search_call_args["limit"] == 20
        assert search_call_args["offset"] == 10

        # Verify filters object was created with correct values
        filters = mock_exercise_service.search_exercises.call_args[0][0]
        assert filters.name == "test"
        assert filters.primary_muscle_group == MuscleGroup.CHEST
        assert filters.movement_pattern == MovementPattern.PUSH
        assert filters.equipment_required == Equipment.BARBELL
        assert filters.difficulty_level == DifficultyLevel.INTERMEDIATE
        assert filters.is_approved is True

    # Statistics Tests

    async def test_get_exercise_statistics_success(self, mock_exercise_service):
        """Test successful exercise statistics retrieval."""
        from app.presentation.api.v1.routes.exercises import get_exercise_statistics

        exercise_id = uuid4()
        mock_stats = {
            "total_uses": 100,
            "average_rating": 4.5,
            "total_ratings": 20,
            "difficulty_distribution": {"beginner": 10, "intermediate": 60, "advanced": 30},
        }
        mock_exercise_service.get_exercise_statistics.return_value = mock_stats

        result = await get_exercise_statistics(
            exercise_id=exercise_id,
            exercise_service=mock_exercise_service,
        )

        mock_exercise_service.get_exercise_statistics.assert_called_once_with(exercise_id)
        assert result.total_uses == mock_stats["total_uses"]
        assert result.average_rating == mock_stats["average_rating"]

    async def test_get_exercise_statistics_not_found(self, mock_exercise_service):
        """Test exercise statistics when exercise not found."""
        from app.presentation.api.v1.routes.exercises import get_exercise_statistics

        exercise_id = uuid4()
        mock_exercise_service.get_exercise_statistics.side_effect = NotFoundError("Exercise not found")

        with pytest.raises(HTTPException) as exc_info:
            await get_exercise_statistics(
                exercise_id=exercise_id,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    # Service Dependency Tests

    async def test_get_exercise_service_dependency(self):
        """Test exercise service dependency injection."""
        from app.presentation.api.v1.routes.exercises import get_exercise_service

        # Mock database session
        mock_session = Mock()

        # Call dependency function
        service = await get_exercise_service(session=mock_session)

        # Verify service is created correctly
        assert service is not None
        assert hasattr(service, 'repository')

    # Integration Tests with Mocked Dependencies

    @patch('app.presentation.api.v1.routes.exercises.get_current_user')
    @patch('app.presentation.api.v1.routes.exercises.get_exercise_service')
    async def test_create_exercise_integration(
        self, mock_get_service, mock_get_user, sample_exercise, exercise_create_request
    ):
        """Test create exercise with mocked dependencies."""
        # Setup mocks
        mock_user = Mock()
        mock_user.id = uuid4()
        mock_get_user.return_value = mock_user

        mock_service = AsyncMock()
        mock_service.create_exercise.return_value = sample_exercise
        mock_get_service.return_value = mock_service

        # Import after patching
        from app.presentation.api.v1.routes.exercises import create_exercise

        # Call endpoint
        result = await create_exercise(
            request=exercise_create_request,
            current_user=mock_user,
            exercise_service=mock_service,
        )

        # Verify result
        assert result.name == sample_exercise.name
        mock_service.create_exercise.assert_called_once()

    # Error Handling Edge Cases

    async def test_update_exercise_validation_error(
        self, mock_exercise_service, mock_current_user
    ):
        """Test exercise update with validation error."""
        from app.presentation.api.v1.routes.exercises import update_exercise

        exercise_id = uuid4()
        update_request = ExerciseUpdateRequest(name="")  # Invalid empty name

        mock_exercise_service.update_exercise.side_effect = ValidationError("Name cannot be empty")

        with pytest.raises(HTTPException) as exc_info:
            await update_exercise(
                exercise_id=exercise_id,
                request=update_request,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    async def test_restore_exercise_business_rule_violation(
        self, mock_exercise_service, mock_current_user
    ):
        """Test exercise restoration with business rule violation."""
        from app.presentation.api.v1.routes.exercises import restore_exercise

        exercise_id = uuid4()
        mock_exercise_service.restore_exercise.side_effect = BusinessRuleViolationError(
            "Exercise is not deleted"
        )

        with pytest.raises(HTTPException) as exc_info:
            await restore_exercise(
                exercise_id=exercise_id,
                current_user=mock_current_user,
                exercise_service=mock_exercise_service,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
