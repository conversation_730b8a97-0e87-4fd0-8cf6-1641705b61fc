"""
Unit tests for exercise repository implementation.

Tests for SQLAlchemy exercise repository implementation including
CRUD operations, search, approval workflows, and statistics.
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, Mock, patch
from uuid import uuid4

import pytest
from sqlalchemy import func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    DifficultyLevel,
    Equipment,
    Exercise,
    ExerciseSearchFilters,
    MovementPattern,
    MuscleGroup,
)
from app.infrastructure.database.models.exercise_model import ExerciseModel
from app.infrastructure.database.repositories.exercise_repository_impl import (
    ExerciseRepositoryImpl,
)


@pytest.mark.unit
@pytest.mark.infrastructure
class TestExerciseRepositoryImpl:
    """Test exercise repository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def exercise_repository(self, mock_session):
        """Create exercise repository with mock session."""
        return ExerciseRepositoryImpl(mock_session)

    @pytest.fixture
    def sample_exercise_model(self):
        """Create sample exercise model."""
        now = datetime.utcnow()
        return ExerciseModel(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST.value,
            secondary_muscle_groups=[
                MuscleGroup.SHOULDERS.value,
                MuscleGroup.TRICEPS.value,
            ],
            movement_pattern=MovementPattern.PUSH.value,
            equipment_required=[Equipment.BARBELL.value, Equipment.MACHINE.value],
            difficulty_level=DifficultyLevel.INTERMEDIATE.value,
            execution_steps=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED.value,
            is_active=True,
            is_approved=True,
            change_reason=ChangeReason.INITIAL_CREATION.value,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )

    @pytest.fixture
    def sample_exercise_entity(self):
        """Create sample exercise entity."""
        now = datetime.utcnow()
        return Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL, Equipment.MACHINE],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            execution_steps=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED,
            is_active=True,
            is_approved=True,
            change_reason=ChangeReason.INITIAL_CREATION,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )

    async def test_create_exercise(self, exercise_repository, mock_session):
        """Test exercise creation."""
        # Create a simple exercise entity with proper enum values
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Test Exercise",
            description="Test description",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            execution_steps=["Step 1"],
            approval_status=ApprovalStatus.PENDING,
            is_active=True,
            is_approved=False,
            change_reason=ChangeReason.INITIAL_CREATION,
            created_by=uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        # Mock the audit logging
        with patch.object(exercise_repository, "log_audit") as mock_audit:
            mock_audit.return_value = None

            result = await exercise_repository.create(exercise)

            assert result == exercise
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
            mock_audit.assert_called_once()

    async def test_get_by_id_found(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test getting exercise by ID when found."""
        exercise_id = sample_exercise_model.id

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_model
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_id(exercise_id)

        assert isinstance(result, Exercise)
        assert result.id == exercise_id
        mock_session.execute.assert_called_once()

    async def test_get_by_id_not_found(self, exercise_repository, mock_session):
        """Test getting exercise by ID when not found."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_id(exercise_id)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_by_uuid_found(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test getting exercise by UUID when found."""
        exercise_uuid = sample_exercise_model.exercise_uuid

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_model
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_uuid(exercise_uuid)

        assert isinstance(result, Exercise)
        assert result.exercise_uuid == exercise_uuid
        mock_session.execute.assert_called_once()

    async def test_get_by_uuid_not_found(self, exercise_repository, mock_session):
        """Test getting exercise by UUID when not found."""
        exercise_uuid = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_uuid(exercise_uuid)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_versions(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test getting all versions of an exercise."""
        exercise_uuid = sample_exercise_model.exercise_uuid

        # Mock query result with multiple versions
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [
            sample_exercise_model,
            sample_exercise_model,
        ]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_versions(exercise_uuid)

        assert len(result) == 2
        assert all(isinstance(ex, Exercise) for ex in result)
        mock_session.execute.assert_called_once()

    async def test_search_exercises(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test exercise search."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters)

        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()

    async def test_search_exercises_with_pagination(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test exercise search with pagination."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters, limit=20, offset=10)

        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()

    async def test_count_exercises(self, exercise_repository, mock_session):
        """Test counting exercises."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 5
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.count(filters)

        assert result == 5
        mock_session.execute.assert_called_once()

    async def test_update_exercise(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test exercise update."""
        # Mock get_by_id to return existing exercise for both initial check and final return
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock log_audit method
            with patch.object(exercise_repository, "log_audit") as mock_audit:
                mock_audit.return_value = None

                # Mock session execute for the update
                mock_result = Mock()
                mock_session.execute.return_value = mock_result

                result = await exercise_repository.update(sample_exercise_entity)

                assert result == sample_exercise_entity
                # The update method calls get_by_id twice - once to check existence and once to return updated entity
                assert mock_get.call_count == 2
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()

    async def test_update_exercise_not_found(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test exercise update when not found."""
        # Mock get_by_id to return None
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = None

            with pytest.raises(ValueError, match="not found"):
                await exercise_repository.update(sample_exercise_entity)

    async def test_soft_delete_exercise(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test exercise soft deletion."""
        exercise_id = uuid4()
        deleted_by = uuid4()

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_by_id to return a proper exercise entity
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock audit logging
            with patch.object(exercise_repository, "log_audit") as mock_audit:
                mock_audit.return_value = None

                result = await exercise_repository.soft_delete(exercise_id, deleted_by)

                assert result is True
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()

    async def test_soft_delete_exercise_not_found(
        self, exercise_repository, mock_session
    ):
        """Test exercise soft deletion when not found."""
        exercise_id = uuid4()
        deleted_by = uuid4()

        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.soft_delete(exercise_id, deleted_by)

        assert result is False
        mock_session.execute.assert_called_once()

    async def test_approve_exercise(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test exercise approval."""
        exercise_id = sample_exercise_entity.id
        approved_by = uuid4()
        notes = "Looks good"

        # Mock get_by_id to return exercise
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock update result
            mock_result = Mock()
            mock_result.rowcount = 1
            mock_session.execute.return_value = mock_result

            # Mock audit logging
            with patch.object(exercise_repository, "log_audit") as mock_audit:
                mock_audit.return_value = None

                result = await exercise_repository.approve(
                    exercise_id, approved_by, notes
                )

                assert result == sample_exercise_entity
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()

    async def test_reject_exercise(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test exercise rejection."""
        exercise_id = sample_exercise_entity.id
        rejected_by = uuid4()
        notes = "Needs improvement"

        # Mock get_by_id to return exercise
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock update result
            mock_result = Mock()
            mock_result.rowcount = 1
            mock_session.execute.return_value = mock_result

            # Mock audit logging
            with patch.object(exercise_repository, "log_audit") as mock_audit:
                mock_audit.return_value = None

                result = await exercise_repository.reject(
                    exercise_id, rejected_by, notes
                )

                assert result == sample_exercise_entity
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()

    async def test_exists_exercise(self, exercise_repository, mock_session):
        """Test checking if exercise exists."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists(exercise_id)

        assert result is True
        mock_session.execute.assert_called_once()

    async def test_not_exists_exercise(self, exercise_repository, mock_session):
        """Test checking if exercise doesn't exist."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists(exercise_id)

        assert result is False
        mock_session.execute.assert_called_once()

    async def test_exists_by_name(self, exercise_repository, mock_session):
        """Test checking if exercise exists by name."""
        name = "Bench Press"

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists_by_name(name)

        assert result is True
        mock_session.execute.assert_called_once()

    async def test_exists_by_name_with_exclusion(
        self, exercise_repository, mock_session
    ):
        """Test checking if exercise exists by name with exclusion."""
        name = "Bench Press"
        exclude_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists_by_name(name, exclude_id)

        assert result is False
        mock_session.execute.assert_called_once()

    async def test_create_version(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test creating a new version of an exercise."""
        exercise_uuid = sample_exercise_entity.exercise_uuid
        created_by = uuid4()

        # Mock get_current_version
        with patch.object(
            exercise_repository, "get_current_version"
        ) as mock_get_current:
            mock_get_current.return_value = sample_exercise_entity

            # Mock create method
            with patch.object(exercise_repository, "create") as mock_create:
                mock_create.return_value = sample_exercise_entity

                result = await exercise_repository.create_version(
                    exercise_uuid, sample_exercise_entity, created_by
                )

                assert result == sample_exercise_entity
                mock_get_current.assert_called_once_with(exercise_uuid)
                mock_create.assert_called_once()

    async def test_get_current_version(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test getting current version of an exercise."""
        exercise_uuid = sample_exercise_entity.exercise_uuid

        # Mock get_by_uuid
        with patch.object(exercise_repository, "get_by_uuid") as mock_get:
            mock_get.return_value = sample_exercise_entity

            result = await exercise_repository.get_current_version(exercise_uuid)

            assert result == sample_exercise_entity
            mock_get.assert_called_once_with(exercise_uuid, current_only=True)

    async def test_set_current_version(self, exercise_repository, mock_session):
        """Test setting current version of an exercise."""
        exercise_uuid = uuid4()
        version = 2
        updated_by = uuid4()

        # Mock update results
        mock_result1 = Mock()
        mock_result1.rowcount = 1
        mock_result2 = Mock()
        mock_result2.rowcount = 1
        mock_session.execute.side_effect = [mock_result1, mock_result2]

        result = await exercise_repository.set_current_version(
            exercise_uuid, version, updated_by
        )

        assert result is True
        assert mock_session.execute.call_count == 2

    async def test_restore_exercise(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test restoring a soft-deleted exercise."""
        exercise_id = uuid4()
        restored_by = uuid4()

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_by_id to return a proper exercise entity
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock audit logging
            with patch.object(exercise_repository, "log_audit") as mock_audit:
                mock_audit.return_value = None

                result = await exercise_repository.restore(exercise_id, restored_by)

                assert result is True
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()

    def test_model_to_entity_conversion(
        self, exercise_repository, sample_exercise_model
    ):
        """Test exercise model to entity conversion."""
        result = exercise_repository._model_to_entity(sample_exercise_model)

        assert isinstance(result, Exercise)
        assert result.id == sample_exercise_model.id
        assert result.name == sample_exercise_model.name
        assert result.primary_muscle_group == MuscleGroup.CHEST
        assert MuscleGroup.SHOULDERS in result.secondary_muscle_groups
        assert result.approval_status == ApprovalStatus.APPROVED
        assert result.movement_pattern == MovementPattern.PUSH
        assert Equipment.BARBELL in result.equipment_required
        assert result.difficulty_level == DifficultyLevel.INTERMEDIATE

    def test_entity_to_dict_conversion(
        self, exercise_repository, sample_exercise_entity
    ):
        """Test exercise entity to dict conversion."""
        result = exercise_repository._entity_to_dict(sample_exercise_entity)

        assert isinstance(result, dict)
        assert result["name"] == sample_exercise_entity.name
        assert result["description"] == sample_exercise_entity.description
        # Since the entity uses use_enum_values=True, the values are already strings
        assert result["primary_muscle_group"] == sample_exercise_entity.primary_muscle_group
        assert result["approval_status"] == sample_exercise_entity.approval_status

    # Additional comprehensive tests for missing functionality

    async def test_search_with_complex_filters(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test exercise search with complex filters."""
        filters = ExerciseSearchFilters(
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS],
            equipment_required=[Equipment.BARBELL],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            is_approved=True,
            is_active=True,
            name_contains="bench"
        )

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters, limit=10, offset=0)

        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()

    async def test_search_with_empty_results(
        self, exercise_repository, mock_session
    ):
        """Test exercise search with no results."""
        from app.domain.entities.exercise import MuscleGroup

        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.QUADS)

        # Mock query result with empty list
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters)

        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_count_with_filters(self, exercise_repository, mock_session):
        """Test counting exercises with filters."""
        filters = ExerciseSearchFilters(
            primary_muscle_group=MuscleGroup.CHEST,
            is_approved=True
        )

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 15
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.count(filters)

        assert result == 15
        mock_session.execute.assert_called_once()

    # Removed get_statistics, bulk_approve, and bulk_reject tests as these methods don't exist in the implementation

    async def test_get_by_uuid_with_specific_version(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test getting exercise by UUID with specific version."""
        exercise_uuid = sample_exercise_model.exercise_uuid
        version = 2

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_model
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_uuid(
            exercise_uuid, version=version, current_only=False
        )

        assert isinstance(result, Exercise)
        assert result.exercise_uuid == exercise_uuid
        mock_session.execute.assert_called_once()

    async def test_create_version_exercise_not_found(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test creating version when exercise doesn't exist."""
        exercise_uuid = uuid4()
        created_by = uuid4()

        # Mock get_current_version to return None
        with patch.object(
            exercise_repository, "get_current_version"
        ) as mock_get_current:
            mock_get_current.return_value = None

            with pytest.raises(ValueError, match="not found"):
                await exercise_repository.create_version(
                    exercise_uuid, sample_exercise_entity, created_by
                )

    # Media management tests

    async def test_add_media(self, exercise_repository, mock_session):
        """Test adding media to an exercise."""
        from app.domain.entities.exercise import ExerciseMedia, MediaType

        exercise_id = uuid4()
        media = ExerciseMedia(
            id=uuid4(),
            exercise_id=exercise_id,
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            title="Exercise Demo",
            description="Demonstration video",
            sort_order=1,
            is_primary=False,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        result = await exercise_repository.add_media(exercise_id, media)

        assert result == media
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()

    async def test_get_media(self, exercise_repository, mock_session):
        """Test getting media for an exercise."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_media(exercise_id)

        assert isinstance(result, list)
        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_update_media(self, exercise_repository, mock_session):
        """Test updating exercise media."""
        from app.domain.entities.exercise import ExerciseMedia, MediaType

        media_id = uuid4()
        updated_media = ExerciseMedia(
            id=media_id,
            exercise_id=uuid4(),
            media_type=MediaType.VIDEO,
            url="https://example.com/updated.mp4",
            title="Updated Title",
            description="Updated description",
            sort_order=1,
            is_primary=False,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Mock update result and get result
        mock_update_result = Mock()
        mock_update_result.rowcount = 1

        # Mock the get result for the updated media
        mock_get_result = Mock()
        mock_get_result.scalar_one_or_none.return_value = Mock(
            id=media_id,
            exercise_id=updated_media.exercise_id,
            media_type=MediaType.VIDEO.value,
            url=updated_media.url,
            thumbnail_url=None,
            title=updated_media.title,
            description=updated_media.description,
            file_size_bytes=None,
            duration_seconds=None,
            width_pixels=None,
            height_pixels=None,
            mime_type=None,
            sort_order=updated_media.sort_order,
            is_primary=False,
            is_active=updated_media.is_active,
            created_at=updated_media.created_at,
            updated_at=updated_media.updated_at
        )

        mock_session.execute.side_effect = [mock_update_result, mock_get_result]

        result = await exercise_repository.update_media(media_id, updated_media)

        assert result.id == media_id
        assert result.title == "Updated Title"
        assert mock_session.execute.call_count == 2

    async def test_delete_media(self, exercise_repository, mock_session):
        """Test deleting exercise media."""
        media_id = uuid4()

        # Mock update result (soft delete)
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.delete_media(media_id)

        assert result is True
        mock_session.execute.assert_called_once()

    # Audit logging tests

    async def test_log_audit(self, exercise_repository, mock_session):
        """Test audit logging."""
        from app.domain.entities.exercise import AuditAction

        exercise_id = uuid4()
        exercise_uuid = uuid4()
        user_id = uuid4()

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        await exercise_repository.log_audit(
            exercise_id=exercise_id,
            exercise_uuid=exercise_uuid,
            action=AuditAction.CREATED,
            user_id=user_id,
            notes="Test audit log"
        )

        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()

    async def test_get_audit_log(self, exercise_repository, mock_session):
        """Test getting audit log for an exercise."""
        exercise_uuid = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_audit_log(exercise_uuid)

        assert isinstance(result, list)
        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_get_audit_log_with_pagination(
        self, exercise_repository, mock_session
    ):
        """Test getting audit log with pagination."""
        exercise_uuid = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_audit_log(
            exercise_uuid, limit=20, offset=10
        )

        assert isinstance(result, list)
        mock_session.execute.assert_called_once()

    # Error handling tests

    async def test_create_exercise_with_database_error(
        self, exercise_repository, mock_session, sample_exercise_entity
    ):
        """Test exercise creation with database error."""
        # Mock session to raise exception
        mock_session.add = Mock()
        mock_session.flush = AsyncMock(side_effect=Exception("Database error"))

        with pytest.raises(Exception, match="Database error"):
            await exercise_repository.create(sample_exercise_entity)

    async def test_get_by_id_with_database_error(
        self, exercise_repository, mock_session
    ):
        """Test get by ID with database error."""
        exercise_id = uuid4()

        # Mock session to raise exception
        mock_session.execute = AsyncMock(side_effect=Exception("Database error"))

        with pytest.raises(Exception, match="Database error"):
            await exercise_repository.get_by_id(exercise_id)

    async def test_search_with_database_error(
        self, exercise_repository, mock_session
    ):
        """Test search with database error."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock session to raise exception
        mock_session.execute = AsyncMock(side_effect=Exception("Database error"))

        with pytest.raises(Exception, match="Database error"):
            await exercise_repository.search(filters)

    # Edge cases and validation tests

    async def test_create_exercise_without_id(
        self, exercise_repository, mock_session
    ):
        """Test creating exercise without ID (should generate one)."""
        # Create exercise with minimal required fields and proper enum values
        now = datetime.utcnow()
        exercise = Exercise(
            id=uuid4(),  # Provide ID since it's required by the model
            exercise_uuid=uuid4(),  # Provide UUID since it's required
            version=1,
            is_current_version=True,
            name="Test Exercise",
            description="Test description",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            approval_status=ApprovalStatus.PENDING,
            is_active=True,
            is_approved=False,
            change_reason=ChangeReason.INITIAL_CREATION,
            created_at=now,
            updated_at=now,
        )

        # Test the ID generation logic by setting them to None after creation
        exercise.id = None
        exercise.exercise_uuid = None

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        # Mock the audit logging
        with patch.object(exercise_repository, "log_audit") as mock_audit:
            mock_audit.return_value = None

            result = await exercise_repository.create(exercise)

            # Should have generated IDs
            assert result.id is not None
            assert result.exercise_uuid is not None
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()

    async def test_search_with_large_limit(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test search with large limit."""
        filters = ExerciseSearchFilters()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model] * 1000
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters, limit=1000, offset=0)

        assert len(result) == 1000
        mock_session.execute.assert_called_once()

    async def test_search_with_zero_limit(
        self, exercise_repository, mock_session
    ):
        """Test search with zero limit."""
        filters = ExerciseSearchFilters()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters, limit=0, offset=0)

        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_approve_nonexistent_exercise(
        self, exercise_repository, mock_session
    ):
        """Test approving non-existent exercise."""
        exercise_id = uuid4()
        approved_by = uuid4()

        # Mock get_by_id to return None
        with patch.object(exercise_repository, "get_by_id") as mock_get:
            mock_get.return_value = None

            # Mock update result
            mock_result = Mock()
            mock_result.rowcount = 1
            mock_session.execute.return_value = mock_result

            result = await exercise_repository.approve(exercise_id, approved_by)

            assert result is None
            mock_session.execute.assert_called_once()

    async def test_set_current_version_failure(
        self, exercise_repository, mock_session
    ):
        """Test setting current version when update fails."""
        exercise_uuid = uuid4()
        version = 2
        updated_by = uuid4()

        # Mock update results - first succeeds, second fails
        mock_result1 = Mock()
        mock_result1.rowcount = 1
        mock_result2 = Mock()
        mock_result2.rowcount = 0  # No rows updated
        mock_session.execute.side_effect = [mock_result1, mock_result2]

        result = await exercise_repository.set_current_version(
            exercise_uuid, version, updated_by
        )

        assert result is False
        assert mock_session.execute.call_count == 2

    # Performance and pagination tests

    async def test_search_with_large_offset(
        self, exercise_repository, mock_session, sample_exercise_model
    ):
        """Test search with large offset."""
        filters = ExerciseSearchFilters()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters, limit=10, offset=10000)

        assert len(result) == 1
        mock_session.execute.assert_called_once()

    async def test_count_with_no_filters(self, exercise_repository, mock_session):
        """Test counting all exercises without filters."""
        filters = ExerciseSearchFilters()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 500
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.count(filters)

        assert result == 500
        mock_session.execute.assert_called_once()

    # Versioning edge cases

    async def test_get_versions_empty_result(
        self, exercise_repository, mock_session
    ):
        """Test getting versions when none exist."""
        exercise_uuid = uuid4()

        # Mock query result with empty list
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_versions(exercise_uuid)

        assert len(result) == 0
        mock_session.execute.assert_called_once()

    async def test_restore_nonexistent_exercise(
        self, exercise_repository, mock_session
    ):
        """Test restoring non-existent exercise."""
        exercise_id = uuid4()
        restored_by = uuid4()

        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.restore(exercise_id, restored_by)

        assert result is False
        mock_session.execute.assert_called_once()

    # Data integrity tests

    async def test_model_to_entity_with_none_values(self, exercise_repository):
        """Test model to entity conversion with None values."""
        result = exercise_repository._model_to_entity(None)
        assert result is None

    async def test_entity_to_dict_with_none_values(self, exercise_repository):
        """Test entity to dict conversion with None entity."""
        result = exercise_repository._entity_to_dict(None)
        assert result == {}

    async def test_media_model_to_entity_conversion(self, exercise_repository):
        """Test media model to entity conversion."""
        from app.infrastructure.database.models.exercise_model import ExerciseMediaModel
        from app.domain.entities.exercise import MediaType

        media_model = ExerciseMediaModel(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.VIDEO.value,
            url="https://example.com/video.mp4",
            title="Test Video",
            description="Test description",
            sort_order=1,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        result = exercise_repository._media_model_to_entity(media_model)

        assert result.id == media_model.id
        assert result.media_type == MediaType.VIDEO
        assert result.url == media_model.url
        assert result.title == media_model.title
