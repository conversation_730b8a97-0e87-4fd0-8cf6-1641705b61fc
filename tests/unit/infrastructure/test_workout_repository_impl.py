"""
Unit tests for workout repository implementation.

Tests for SQLAlchemy workout repository implementation including
CRUD operations, search, and statistics.
"""

from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.entities.workout import (
    ExerciseSet,
    ExerciseSetCreateRequest,
    ExerciseSetUpdateRequest,
    Workout,
    WorkoutCreateRequest,
    WorkoutSearchFilters,
    WorkoutStatus,
    WorkoutUpdateRequest,
)
from app.infrastructure.database.models.workout_model import (
    ExerciseSetModel,
    WorkoutModel,
)
from app.infrastructure.database.repositories.workout_repository_impl import (
    WorkoutRepositoryImpl,
)


@pytest.mark.unit
@pytest.mark.infrastructure
class TestWorkoutRepositoryImpl:
    """Test workout repository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def workout_repository(self, mock_session):
        """Create workout repository with mock session."""
        return WorkoutRepositoryImpl(mock_session)

    @pytest.fixture
    def sample_workout_model(self):
        """Create sample workout model."""
        now = datetime.utcnow()
        return WorkoutModel(
            id=uuid4(),
            user_id=uuid4(),
            name="Test Workout",
            workout_date=now,
            started_at=None,
            completed_at=None,
            total_volume_load=None,
            average_rpe=None,
            total_sets=0,
            total_exercises=0,
            duration_minutes=None,
            notes=None,
            is_template=False,
            template_name=None,
            created_at=now,
            updated_at=now,
            deleted_at=None,
            exercise_sets=[],
        )

    @pytest.fixture
    def sample_exercise_set_model(self):
        """Create sample exercise set model."""
        now = datetime.utcnow()
        return ExerciseSetModel(
            id=uuid4(),
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
            reps_target=None,
            rir_target=None,
            rir_actual=None,
            rpe=None,
            rest_seconds=None,
            tempo=None,
            range_of_motion=None,
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            notes=None,
            created_at=now,
            updated_at=now,
        )

    async def test_create_workout(self, workout_repository, mock_session):
        """Test workout creation."""
        user_id = uuid4()
        request = WorkoutCreateRequest(
            name="Test Workout",
            workout_date=datetime.utcnow(),
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        # Mock the _model_to_entity method to return a proper workout entity
        with patch.object(workout_repository, "_model_to_entity") as mock_convert:
            expected_workout = Workout(
                id=uuid4(),
                user_id=user_id,
                name=request.name,
                workout_date=request.workout_date,
                started_at=None,
                completed_at=None,
                total_volume_load=None,
                average_rpe=None,
                total_sets=0,
                total_exercises=0,
                duration_minutes=None,
                notes=None,
                is_template=False,
                template_name=None,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                deleted_at=None,
                exercise_sets=[],
            )
            mock_convert.return_value = expected_workout

            result = await workout_repository.create_workout(request, user_id)

            assert isinstance(result, Workout)
            assert result.name == request.name
            assert result.user_id == user_id
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
            mock_convert.assert_called_once()

    async def test_get_workout_by_id_found(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test getting workout by ID when found."""
        workout_id = sample_workout_model.id

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_workout_model
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_workout_by_id(workout_id)

        assert isinstance(result, Workout)
        assert result.id == workout_id
        mock_session.execute.assert_called_once()

    async def test_get_workout_by_id_not_found(self, workout_repository, mock_session):
        """Test getting workout by ID when not found."""
        workout_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_workout_by_id(workout_id)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_update_workout(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test workout update."""
        workout_id = sample_workout_model.id
        request = WorkoutUpdateRequest(name="Updated Workout")

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_workout_by_id for return value
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_workout = Workout(
                id=workout_id,
                user_id=uuid4(),
                name="Updated Workout",
                workout_date=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_workout

            result = await workout_repository.update_workout(workout_id, request)

            assert result == mock_workout
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(workout_id)

    async def test_update_workout_not_found(self, workout_repository, mock_session):
        """Test workout update when not found."""
        workout_id = uuid4()
        request = WorkoutUpdateRequest(name="Updated Workout")

        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await workout_repository.update_workout(workout_id, request)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_delete_workout(self, workout_repository, mock_session):
        """Test workout deletion."""
        workout_id = uuid4()

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        result = await workout_repository.delete_workout(workout_id)

        assert result is True
        mock_session.execute.assert_called_once()

    async def test_delete_workout_not_found(self, workout_repository, mock_session):
        """Test workout deletion when not found."""
        workout_id = uuid4()

        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await workout_repository.delete_workout(workout_id)

        assert result is False
        mock_session.execute.assert_called_once()

    async def test_search_workouts(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test workout search."""
        filters = WorkoutSearchFilters(user_id=uuid4())

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_workout_model]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.search_workouts(filters)

        assert len(result) == 1
        assert isinstance(result[0], Workout)
        mock_session.execute.assert_called_once()

    async def test_count_workouts(self, workout_repository, mock_session):
        """Test workout count."""
        filters = WorkoutSearchFilters(user_id=uuid4())

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 5
        mock_session.execute.return_value = mock_result

        result = await workout_repository.count_workouts(filters)

        assert result == 5
        mock_session.execute.assert_called_once()

    async def test_get_active_workout(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test getting active workout."""
        user_id = uuid4()

        # Set workout as in progress
        sample_workout_model.started_at = datetime.utcnow()
        sample_workout_model.completed_at = None

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_workout_model
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_active_workout(user_id)

        assert isinstance(result, Workout)
        assert result.status == WorkoutStatus.IN_PROGRESS
        mock_session.execute.assert_called_once()

    async def test_start_workout(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test starting workout."""
        workout_id = sample_workout_model.id

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_workout_by_id for return value
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_workout = Workout(
                id=workout_id,
                user_id=uuid4(),
                name="Test Workout",
                workout_date=datetime.utcnow(),
                started_at=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_workout

            result = await workout_repository.start_workout(workout_id)

            assert result == mock_workout
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(workout_id)

    async def test_complete_workout(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test completing workout."""
        workout_id = sample_workout_model.id

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_workout_by_id and _update_workout_statistics
        with (
            patch.object(workout_repository, "get_workout_by_id") as mock_get,
            patch.object(
                workout_repository, "_update_workout_statistics"
            ) as mock_update_stats,
        ):

            mock_workout = Workout(
                id=workout_id,
                user_id=uuid4(),
                name="Test Workout",
                workout_date=datetime.utcnow(),
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_workout
            mock_update_stats.return_value = None

            result = await workout_repository.complete_workout(workout_id)

            assert result == mock_workout
            mock_session.execute.assert_called_once()
            mock_get.assert_called()  # Called twice - once after update, once after stats update
            mock_update_stats.assert_called_once_with(workout_id)

    async def test_create_exercise_set(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test exercise set creation."""
        workout_id = uuid4()
        request = ExerciseSetCreateRequest(
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        with patch(
            "app.infrastructure.database.repositories.workout_repository_impl.uuid4"
        ) as mock_uuid:
            mock_uuid.return_value = sample_exercise_set_model.id

            result = await workout_repository.create_exercise_set(workout_id, request)

            assert isinstance(result, ExerciseSet)
            assert result.workout_id == workout_id
            assert result.exercise_id == request.exercise_id
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()

    async def test_get_exercise_set_by_id(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test getting exercise set by ID."""
        set_id = sample_exercise_set_model.id

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_set_model
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_exercise_set_by_id(set_id)

        assert isinstance(result, ExerciseSet)
        assert result.id == set_id
        mock_session.execute.assert_called_once()

    async def test_update_exercise_set(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test exercise set update."""
        set_id = sample_exercise_set_model.id
        request = ExerciseSetUpdateRequest(weight_kg=Decimal("120.0"))

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_exercise_set_by_id for return value
        with patch.object(workout_repository, "get_exercise_set_by_id") as mock_get:
            mock_set = ExerciseSet(
                id=set_id,
                workout_id=uuid4(),
                exercise_id=uuid4(),
                set_number=1,
                weight_kg=Decimal("120.0"),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_set

            result = await workout_repository.update_exercise_set(set_id, request)

            assert result == mock_set
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(set_id)

    async def test_delete_exercise_set(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test exercise set deletion."""
        set_id = sample_exercise_set_model.id

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_set_model
        mock_session.execute.return_value = mock_result
        mock_session.delete = AsyncMock()

        result = await workout_repository.delete_exercise_set(set_id)

        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.delete.assert_called_once_with(sample_exercise_set_model)

    async def test_delete_exercise_set_not_found(
        self, workout_repository, mock_session
    ):
        """Test exercise set deletion when not found."""
        set_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await workout_repository.delete_exercise_set(set_id)

        assert result is False
        mock_session.execute.assert_called_once()

    async def test_get_workout_sets(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test getting workout sets."""
        workout_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_set_model]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_workout_sets(workout_id)

        assert len(result) == 1
        assert isinstance(result[0], ExerciseSet)
        mock_session.execute.assert_called_once()

    async def test_model_to_entity_conversion(
        self, workout_repository, sample_workout_model
    ):
        """Test workout model to entity conversion."""
        result = workout_repository._model_to_entity(sample_workout_model)

        assert isinstance(result, Workout)
        assert result.id == sample_workout_model.id
        assert result.user_id == sample_workout_model.user_id
        assert result.name == sample_workout_model.name

    async def test_set_model_to_entity_conversion(
        self, workout_repository, sample_exercise_set_model
    ):
        """Test exercise set model to entity conversion."""
        result = workout_repository._set_model_to_entity(sample_exercise_set_model)

        assert isinstance(result, ExerciseSet)
        assert result.id == sample_exercise_set_model.id
        assert result.workout_id == sample_exercise_set_model.workout_id
        assert result.exercise_id == sample_exercise_set_model.exercise_id

    # Template operations tests

    async def test_get_workout_templates(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test getting workout templates."""
        user_id = uuid4()

        # Set as template
        sample_workout_model.is_template = True
        sample_workout_model.template_name = "Test Template"

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_workout_model]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_workout_templates(user_id)

        assert len(result) == 1
        assert isinstance(result[0], Workout)
        assert result[0].is_template is True
        mock_session.execute.assert_called_once()

    async def test_copy_workout_as_template(
        self, workout_repository, mock_session, sample_workout_model, sample_exercise_set_model
    ):
        """Test copying workout as template."""
        workout_id = sample_workout_model.id
        template_name = "My Template"

        # Add exercise sets to the original workout
        sample_workout_model.exercise_sets = [sample_exercise_set_model]

        # Mock get_workout_by_id to return original workout
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.side_effect = [
                # First call returns original workout
                workout_repository._model_to_entity(sample_workout_model),
                # Second call returns the created template
                Workout(
                    id=uuid4(),
                    user_id=sample_workout_model.user_id,
                    name=template_name,
                    workout_date=datetime.utcnow(),
                    is_template=True,
                    template_name=template_name,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    exercise_sets=[],
                )
            ]

            # Mock create_workout and create_exercise_set
            with (
                patch.object(workout_repository, "create_workout") as mock_create_workout,
                patch.object(workout_repository, "create_exercise_set") as mock_create_set,
            ):
                template_workout = Workout(
                    id=uuid4(),
                    user_id=sample_workout_model.user_id,
                    name=template_name,
                    workout_date=datetime.utcnow(),
                    is_template=True,
                    template_name=template_name,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    exercise_sets=[],
                )
                mock_create_workout.return_value = template_workout
                mock_create_set.return_value = workout_repository._set_model_to_entity(sample_exercise_set_model)

                result = await workout_repository.copy_workout_as_template(workout_id, template_name)

                assert result is not None
                mock_create_workout.assert_called_once()
                mock_create_set.assert_called_once()
                assert mock_get.call_count == 2

    async def test_copy_workout_as_template_not_found(
        self, workout_repository, mock_session
    ):
        """Test copying workout as template when original not found."""
        workout_id = uuid4()
        template_name = "My Template"

        # Mock get_workout_by_id to return None
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.return_value = None

            result = await workout_repository.copy_workout_as_template(workout_id, template_name)

            assert result is None
            mock_get.assert_called_once_with(workout_id)

    async def test_create_workout_from_template(
        self, workout_repository, mock_session, sample_workout_model, sample_exercise_set_model
    ):
        """Test creating workout from template."""
        template_id = sample_workout_model.id
        user_id = uuid4()
        workout_date = "2024-01-15T10:00:00"

        # Set as template and add exercise sets
        sample_workout_model.is_template = True
        sample_workout_model.template_name = "Test Template"
        sample_workout_model.exercise_sets = [sample_exercise_set_model]

        # Mock get_workout_by_id to return template
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.side_effect = [
                # First call returns template
                workout_repository._model_to_entity(sample_workout_model),
                # Second call returns the created workout
                Workout(
                    id=uuid4(),
                    user_id=user_id,
                    name=sample_workout_model.name,
                    workout_date=datetime.fromisoformat(workout_date),
                    is_template=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    exercise_sets=[],
                )
            ]

            # Mock create_workout and create_exercise_set
            with (
                patch.object(workout_repository, "create_workout") as mock_create_workout,
                patch.object(workout_repository, "create_exercise_set") as mock_create_set,
            ):
                new_workout = Workout(
                    id=uuid4(),
                    user_id=user_id,
                    name=sample_workout_model.name,
                    workout_date=datetime.fromisoformat(workout_date),
                    is_template=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    exercise_sets=[],
                )
                mock_create_workout.return_value = new_workout
                mock_create_set.return_value = workout_repository._set_model_to_entity(sample_exercise_set_model)

                result = await workout_repository.create_workout_from_template(
                    template_id, user_id, workout_date
                )

                assert result is not None
                assert result.is_template is False
                mock_create_workout.assert_called_once()
                mock_create_set.assert_called_once()
                assert mock_get.call_count == 2

    async def test_create_workout_from_template_not_found(
        self, workout_repository, mock_session
    ):
        """Test creating workout from template when template not found."""
        template_id = uuid4()
        user_id = uuid4()

        # Mock get_workout_by_id to return None
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.return_value = None

            result = await workout_repository.create_workout_from_template(template_id, user_id)

            assert result is None
            mock_get.assert_called_once_with(template_id)

    async def test_create_workout_from_template_not_template(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test creating workout from template when source is not a template."""
        template_id = sample_workout_model.id
        user_id = uuid4()

        # Set as regular workout (not template)
        sample_workout_model.is_template = False

        # Mock get_workout_by_id to return non-template workout
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.return_value = workout_repository._model_to_entity(sample_workout_model)

            result = await workout_repository.create_workout_from_template(template_id, user_id)

            assert result is None
            mock_get.assert_called_once_with(template_id)

    # Statistics tests

    async def test_get_workout_statistics(
        self, workout_repository, mock_session, sample_workout_model, sample_exercise_set_model
    ):
        """Test getting workout statistics."""
        workout_id = sample_workout_model.id

        # Set up workout with statistics
        sample_workout_model.total_sets = 3
        sample_workout_model.total_exercises = 2
        sample_workout_model.total_volume_load = Decimal("1500.0")
        sample_workout_model.average_rpe = Decimal("7.5")
        sample_workout_model.duration_minutes = 45

        # Create multiple exercise sets with different values
        set1 = ExerciseSetModel(
            id=uuid4(),
            workout_id=workout_id,
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        set2 = ExerciseSetModel(
            id=uuid4(),
            workout_id=workout_id,
            exercise_id=uuid4(),
            set_number=2,
            weight_kg=Decimal("120.0"),
            reps_completed=8,
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        sample_workout_model.exercise_sets = [set1, set2]

        # Mock get_workout_by_id
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.return_value = workout_repository._model_to_entity(sample_workout_model)

            result = await workout_repository.get_workout_statistics(workout_id)

            assert result["total_sets"] == 3
            assert result["total_exercises"] == 2
            assert result["total_volume_load"] == 1500.0
            assert result["average_rpe"] == 7.5
            assert result["duration_minutes"] == 45
            assert result["max_weight"] == 120.0
            assert result["total_reps"] == 18
            mock_get.assert_called_once_with(workout_id)

    async def test_get_workout_statistics_not_found(
        self, workout_repository, mock_session
    ):
        """Test getting statistics for non-existent workout."""
        workout_id = uuid4()

        # Mock get_workout_by_id to return None
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.return_value = None

            result = await workout_repository.get_workout_statistics(workout_id)

            assert result == {}
            mock_get.assert_called_once_with(workout_id)

    async def test_update_workout_statistics(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test updating workout statistics."""
        workout_id = uuid4()

        # Create exercise sets with different values
        set1 = sample_exercise_set_model
        set1.weight_kg = Decimal("100.0")
        set1.reps_completed = 10
        set1.rpe = Decimal("8.0")

        set2 = ExerciseSetModel(
            id=uuid4(),
            workout_id=workout_id,
            exercise_id=uuid4(),  # Different exercise
            set_number=2,
            weight_kg=Decimal("120.0"),
            reps_completed=8,
            rpe=Decimal("7.0"),
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        # Mock get_workout_sets
        with patch.object(workout_repository, "get_workout_sets") as mock_get_sets:
            mock_get_sets.return_value = [
                workout_repository._set_model_to_entity(set1),
                workout_repository._set_model_to_entity(set2),
            ]

            # Mock session execute for update
            mock_result = Mock()
            mock_session.execute.return_value = mock_result

            await workout_repository._update_workout_statistics(workout_id)

            # Verify statistics calculation
            mock_get_sets.assert_called_once_with(workout_id)
            mock_session.execute.assert_called_once()

            # Check that update was called with correct values
            call_args = mock_session.execute.call_args[0][0]
            # Total volume should be (100*10) + (120*8) = 1960
            # Average RPE should be (8.0 + 7.0) / 2 = 7.5
            # Total sets should be 2
            # Total exercises should be 2 (different exercise_ids)

    async def test_update_workout_statistics_no_sets(
        self, workout_repository, mock_session
    ):
        """Test updating workout statistics when no sets exist."""
        workout_id = uuid4()

        # Mock get_workout_sets to return empty list
        with patch.object(workout_repository, "get_workout_sets") as mock_get_sets:
            mock_get_sets.return_value = []

            await workout_repository._update_workout_statistics(workout_id)

            # Should return early without updating
            mock_get_sets.assert_called_once_with(workout_id)
            mock_session.execute.assert_not_called()

    # Search filter edge cases

    async def test_search_workouts_with_all_filters(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test workout search with all possible filters."""
        from datetime import date

        filters = WorkoutSearchFilters(
            user_id=uuid4(),
            is_template=False,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 12, 31),
            name_contains="Test",
            status=WorkoutStatus.COMPLETED,
        )

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_workout_model]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.search_workouts(filters, limit=10, offset=5)

        assert len(result) == 1
        assert isinstance(result[0], Workout)
        mock_session.execute.assert_called_once()

    async def test_search_workouts_with_status_planned(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test workout search with planned status filter."""
        filters = WorkoutSearchFilters(status=WorkoutStatus.PLANNED)

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_workout_model]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.search_workouts(filters)

        assert len(result) == 1
        mock_session.execute.assert_called_once()

    async def test_search_workouts_with_status_in_progress(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test workout search with in_progress status filter."""
        filters = WorkoutSearchFilters(status=WorkoutStatus.IN_PROGRESS)

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_workout_model]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.search_workouts(filters)

        assert len(result) == 1
        mock_session.execute.assert_called_once()

    async def test_count_workouts_with_all_filters(
        self, workout_repository, mock_session
    ):
        """Test workout count with all possible filters."""
        from datetime import date

        filters = WorkoutSearchFilters(
            user_id=uuid4(),
            is_template=True,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 12, 31),
            name_contains="Template",
        )

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 3
        mock_session.execute.return_value = mock_result

        result = await workout_repository.count_workouts(filters)

        assert result == 3
        mock_session.execute.assert_called_once()

    async def test_get_user_workouts(
        self, workout_repository, mock_session, sample_workout_model
    ):
        """Test getting user workouts (non-templates)."""
        user_id = uuid4()

        # Mock search_workouts method
        with patch.object(workout_repository, "search_workouts") as mock_search:
            expected_workouts = [workout_repository._model_to_entity(sample_workout_model)]
            mock_search.return_value = expected_workouts

            result = await workout_repository.get_user_workouts(user_id, limit=25, offset=10)

            assert result == expected_workouts
            mock_search.assert_called_once()

            # Verify the filters passed to search_workouts
            call_args = mock_search.call_args
            filters = call_args[0][0]
            assert filters.user_id == user_id
            assert filters.is_template is False
            # Check positional arguments for limit and offset
            assert call_args[0][1] == 25  # limit
            assert call_args[0][2] == 10  # offset

    # Exercise set operations tests

    async def test_get_exercise_sets_by_exercise(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test getting exercise sets for specific exercise in workout."""
        workout_id = uuid4()
        exercise_id = uuid4()

        # Create multiple sets for the same exercise
        set1 = sample_exercise_set_model
        set1.workout_id = workout_id
        set1.exercise_id = exercise_id
        set1.set_number = 1

        set2 = ExerciseSetModel(
            id=uuid4(),
            workout_id=workout_id,
            exercise_id=exercise_id,
            set_number=2,
            weight_kg=Decimal("110.0"),
            reps_completed=8,
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [set1, set2]
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_exercise_sets_by_exercise(workout_id, exercise_id)

        assert len(result) == 2
        assert all(isinstance(s, ExerciseSet) for s in result)
        assert all(s.workout_id == workout_id for s in result)
        assert all(s.exercise_id == exercise_id for s in result)
        mock_session.execute.assert_called_once()

    async def test_update_exercise_set_with_all_fields(
        self, workout_repository, mock_session, sample_exercise_set_model
    ):
        """Test exercise set update with all possible fields."""
        set_id = sample_exercise_set_model.id
        request = ExerciseSetUpdateRequest(
            weight_kg=Decimal("125.0"),
            reps_completed=12,
            reps_target=10,
            rir_target=2,
            rir_actual=1,
            rpe=Decimal("8.5"),
            rest_seconds=90,
            tempo="3-1-2-0",
            range_of_motion="full",
            is_warmup=True,
            is_dropset=True,
            is_failure=False,
            notes="Great set!",
        )

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_exercise_set_by_id for return value
        with patch.object(workout_repository, "get_exercise_set_by_id") as mock_get:
            updated_set = ExerciseSet(
                id=set_id,
                workout_id=uuid4(),
                exercise_id=uuid4(),
                set_number=1,
                weight_kg=request.weight_kg,
                reps_completed=request.reps_completed,
                reps_target=request.reps_target,
                rir_target=request.rir_target,
                rir_actual=request.rir_actual,
                rpe=request.rpe,
                rest_seconds=request.rest_seconds,
                tempo=request.tempo,
                range_of_motion=request.range_of_motion,
                is_warmup=request.is_warmup,
                is_dropset=request.is_dropset,
                is_failure=request.is_failure,
                notes=request.notes,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = updated_set

            result = await workout_repository.update_exercise_set(set_id, request)

            assert result == updated_set
            assert result.weight_kg == request.weight_kg
            assert result.reps_completed == request.reps_completed
            assert result.is_warmup == request.is_warmup
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(set_id)

    async def test_update_exercise_set_not_found(
        self, workout_repository, mock_session
    ):
        """Test exercise set update when set not found."""
        set_id = uuid4()
        request = ExerciseSetUpdateRequest(weight_kg=Decimal("125.0"))

        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await workout_repository.update_exercise_set(set_id, request)

        assert result is None
        mock_session.execute.assert_called_once()

    # Error handling and edge cases

    async def test_start_workout_already_started(
        self, workout_repository, mock_session
    ):
        """Test starting workout that's already started."""
        workout_id = uuid4()

        # Mock update result with no rows affected (workout already started)
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await workout_repository.start_workout(workout_id)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_complete_workout_not_started(
        self, workout_repository, mock_session
    ):
        """Test completing workout that hasn't been started."""
        workout_id = uuid4()

        # Mock update result with no rows affected (workout not started)
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await workout_repository.complete_workout(workout_id)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_complete_workout_no_statistics_update(
        self, workout_repository, mock_session
    ):
        """Test completing workout when get_workout_by_id returns None after completion."""
        workout_id = uuid4()

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock get_workout_by_id to return None (workout not found after completion)
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.return_value = None

            result = await workout_repository.complete_workout(workout_id)

            assert result is None
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(workout_id)

    async def test_get_active_workout_none_found(
        self, workout_repository, mock_session
    ):
        """Test getting active workout when none exists."""
        user_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_active_workout(user_id)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_exercise_set_by_id_not_found(
        self, workout_repository, mock_session
    ):
        """Test getting exercise set by ID when not found."""
        set_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await workout_repository.get_exercise_set_by_id(set_id)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_count_workouts_returns_none(
        self, workout_repository, mock_session
    ):
        """Test workout count when query returns None."""
        filters = WorkoutSearchFilters(user_id=uuid4())

        # Mock query result returning None
        mock_result = Mock()
        mock_result.scalar.return_value = None
        mock_session.execute.return_value = mock_result

        result = await workout_repository.count_workouts(filters)

        assert result == 0
        mock_session.execute.assert_called_once()

    async def test_create_workout_from_template_default_date(
        self, workout_repository, mock_session, sample_workout_model, sample_exercise_set_model
    ):
        """Test creating workout from template with default date."""
        template_id = sample_workout_model.id
        user_id = uuid4()

        # Set as template
        sample_workout_model.is_template = True
        sample_workout_model.template_name = "Test Template"
        sample_workout_model.exercise_sets = [sample_exercise_set_model]

        # Mock get_workout_by_id to return template
        with patch.object(workout_repository, "get_workout_by_id") as mock_get:
            mock_get.side_effect = [
                # First call returns template
                workout_repository._model_to_entity(sample_workout_model),
                # Second call returns the created workout
                Workout(
                    id=uuid4(),
                    user_id=user_id,
                    name=sample_workout_model.name,
                    workout_date=datetime.utcnow(),
                    is_template=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    exercise_sets=[],
                )
            ]

            # Mock create_workout and create_exercise_set
            with (
                patch.object(workout_repository, "create_workout") as mock_create_workout,
                patch.object(workout_repository, "create_exercise_set") as mock_create_set,
            ):
                new_workout = Workout(
                    id=uuid4(),
                    user_id=user_id,
                    name=sample_workout_model.name,
                    workout_date=datetime.utcnow(),
                    is_template=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    exercise_sets=[],
                )
                mock_create_workout.return_value = new_workout
                mock_create_set.return_value = workout_repository._set_model_to_entity(sample_exercise_set_model)

                # Call without workout_date parameter (should use default)
                result = await workout_repository.create_workout_from_template(template_id, user_id)

                assert result is not None
                assert result.is_template is False
                mock_create_workout.assert_called_once()
                mock_create_set.assert_called_once()
                assert mock_get.call_count == 2
